// i18n.js
import {
	createI18n
} from 'vue-i18n';

import enLocal from './locales/en.json'
import cnLocal from './locales/cn.json'
import idLocal from './locales/id.json'


let language = uni.getStorageSync('lang') || 'id'; //  获取本地存储 || 根据浏览器语言设置


const messages = {
	en: enLocal,
	zh: cnLocal,
	id: idLocal
};

const i18n = createI18n({
	locale: language, // 设置默认语言
	messages, // 设置资源
});

export default i18n;