image {
	width: 100%;
	height: 100%;
}

ul {
	list-style: none;
	padding: 0;
	margin: 0;
}
							@keyframes example {
							  0% {opacity: 0;}
							  25% {opacity: 0.2;}
							  50% {opacity: 0.5;}
							  70% {opacity: 0.7;}
							  100% {opacity: 1;}
							}
							@keyframes example1 {
							  0% {opacity: 1;}
							  25% {opacity: 0.7;}
							  50% {opacity: 0.5;}
							   70% {opacity: 0.2;}
							  100% {opacity: 0;}
							}

.wrapper {
	position: relative;
}
	.content {
		display: flex;
		flex-direction: column;
		align-items: center;
		/* justify-content: center; */
	}
	.index_body {
		width: 100%;
		height: auto;
		min-height: 300rpx;
		background-color: #000000;
		padding-top: 60rpx;
		box-sizing: border-box;
		// 导航start
		.header {
			width: 100%;
			height: 170rpx;
			padding: 0 100rpx;
			box-sizing: border-box;
			background-color: #000000;
			z-index: 999;
			position: fixed;
			@media (max-width: 1280px) {
				padding-left: 0;
				    padding-right: 0;
			}
			.header_box {
				width: 100%;
				height: 100%;
				padding: 0 156rpx;
				box-sizing: border-box;
				display: flex;
				flex-direction: row;
				align-items: center;
				justify-content: space-between;
				border: 1px solid #262626;
				border-radius: 24rpx;
				@media (max-width: 1650px) {
					padding: 0 20rpx;
				}
				.logo_appname_boxbody {
					width: 426rpx;
					height: 100%;
					padding: 26rpx 0;
					box-sizing: border-box;
					@media (max-width: 1080px) {
						width: auto;
					}
					.logo_appname_box {
						width: 100%;
						height: 100%;
						padding: 14rpx 17rpx;
						box-sizing: border-box;
						display: flex;
						flex-direction: row;
						align-items: center;
						justify-content: space-between;
						.logo_box {
							height: 100%;
							aspect-ratio: 1 / 1;
							border-radius: 20rpx;
							overflow: hidden;
						}
						.appname_box {
							// width: 100%;
							flex: 1;
							height: 100%;
							padding-left: 22rpx;
							box-sizing: border-box;
							display: flex;
							flex-direction: column;
							.appname {
								flex: 1;
								// height: auto;
								display: flex;
								align-items: center;
								font-size: 30rpx;
								color: white;
								font-weight: bold;
								@media (max-width: 1080px) {
									font-size: 24rpx;
								}
							}
							.log_tit {
								flex: 1;
								display: flex;
								align-items: center;
								width: 100%;
								// height: auto;
								font-size: 24rpx;
								color: #8E8E92;
								@media (max-width: 1080px) {
									font-size: 14rpx;
								}
							}
						}
					}
				}
				.header_menu_boxbody {
					flex: 1;
					height: 100%;
					display: flex;
					flex-direction: row;
					padding-left: 98rpx;
					padding-right: 98rpx;
					box-sizing: border-box;
					align-items: center;
					@media (max-width: 1650px) {
						padding: 0 20rpx;
					}
											// justify-content: space-between;
					.header_menu_box {
						width: auto;
						height: auto;
						display: flex;
						flex-direction: column;
						align-items: center;
						margin-right: 116rpx;
						cursor: pointer;
						@media (max-width: 1280px) {
							margin-right: 80rpx;
						}
						@media (max-width: 1080px) {
							margin-right: 40rpx;
						}
						.menu_tit {
							height: 54rpx;
							width: auto;
							color: white;
							font-size: 26rpx;
							display: flex;
							// flex-direction: row;
							align-items: center;
							.hot_download {
								width: auto;
								height: 100%;
								display: flex;
								flex-direction: row;
								align-items: center;
								.hotimg {
									width: 20rpx;
									height: 26rpx;
									margin-right: 10rpx;
								}
							}
						}
					}
					.header_menu_box.active {
						.menu_tit {
							color: #BAFC51;
						}
						.a_liner {
							width: 18rpx;
							height: 4rpx;
							border-radius: 24rpx;
							background-color: #BAFC51;
						}
					}
				}
				.lang_contact_box {
					width: 590rpx;
					height: 100%;
					display: flex;
					flex-direction: row;
					padding: 20rpx 0;
					box-sizing: border-box;
					align-items: center;
					justify-content: space-between;
					.lang_box_body {
						width: auto;
						height: 100%;
						display: flex;
						flex-direction: row;
						align-items: center;
						.lang_imgbox {
							width: 40rpx;
							height: 40rpx;
							margin-right: 8rpx;
						}
						.lang_choose_boxbody {
							width: auto;
							height: auto;
							display: flex;
							flex-direction: row;
							align-items: center;
							// margin-left: 8rpx;
							white-space: nowrap;
							.lang_mes {
								font-size: 32rpx;
								color: rgba(255, 255, 255, .6);
							}
							.xiala {
								font-size: 32rpx;
								color: rgba(255, 255, 255, .6);
							}
						}
					}
					.lang_box_body:hover .langchange_box_body {
						display: flex;
					}
					.contact_box {
						width: 272rpx;
						height: 86rpx;
						background-color: #BAFC51;
						border-radius: 47rpx;
						display: flex;
						align-items: center;
						justify-content: center;
						cursor: pointer;
						position: relative;
						overflow: hidden;
						.contxt {
							font-size: 28rpx;
							font-weight: bold;
						}
						.zzc {
							width: 100%;
							height: 100%;
							background-color: #000000;
							opacity: 0;
							position: absolute;
							top: 0;
							left: 0;
						}
					}
					.contact_box:hover {
						.zzc {
							opacity: .3;
						}
					}
				}
			}
		}
		.langchange_box_body {
			position: fixed;
			width: 320rpx;
			height: auto;
			background-color: #1C1C1D;
			border-radius: 20rpx;
			top: 130rpx;
			right: 550rpx;
			display: none;
			flex-direction: column;
			z-index: 999;
			.lang_box {
				width: 100%;
				height: 87rpx;
				color: white;
				font-size: 32rpx;
				display: flex;
				align-items: center;
				justify-content: center;
			}
			.lang_box:hover {
				background-color: white;
				color: #000000;
			}
		}
		.pc_banner_boxbody {
			width: 100%;
			height: 1800rpx;
			// padding: 0 340rpx;
			padding-top: 230rpx;
			box-sizing: border-box;
			display: flex;
			flex-direction: column;
			align-items: center;
			position: relative;
			.spswiper {
				width: 82%;
				height: 100%;
				display: flex;
				flex-direction: column;
				align-items: center;
				.banner_boxbody {
					width: 100%;
					height: 100%;
					display: flex;
					align-items: center;
					flex-direction: column;
					position: relative;
					.top_tit_mes_box {
						width: 61%;
						height: 480rpx;
						// padding: 0 578rpx;
						padding-top: 76rpx;
						box-sizing: border-box;
						display: flex;
						justify-content: center;
						.top_tit_mes {
							width: 82%;
							height: 100%;
							// padding: 0 170rpx;
							box-sizing: border-box;
							display: flex;
							flex-direction: column;
							justify-content: space-between;
							.top_tit {
								width: 100%;
								height: auto;
								white-space: nowrap;
								font-size: 152rpx;
								font-weight: bold;
								color: white;
								@media (max-width: 1650px) {
									font-size: 130rpx;
								}
								@media (max-width: 1550px) {
									font-size: 120rpx;
								}
								@media (max-width: 1450px) {
									font-size: 110rpx;
								}
								@media (max-width: 1300px) {
									font-size: 100rpx;
								}
							}

							.mes_box {
								flex: 1;
								font-size: 40rpx;
								color: rgba(255, 255, 255, .8);
								text-align: center;
								display: flex;
								align-items: center;
								// @media (max-width: 1650px) {
								// 	font-size: 35rpx;
								// }
								// @media (max-width: 1550px) {
								// 	font-size: 30rpx;
								// }
								// @media (max-width: 1450px) {
								// 	font-size: 25rpx;
								// }
								@media (max-width: 1080px) {
									font-size: 30rpx;
									align-items: flex-start;
								}
							}
						}
					}
					// .banner_box_bgbody {
					// 	width: 100%;
					// 	height: 100%;
					// }
					.banner_swiper_boxbody {
						// flex: 1;
						width: 100%;
						height: auto;
					}
					.banner_swiper_boxbody_b3 {
						height: 1600rpx;
						width: 100%;
						position: relative;
						.tit_mes_zinx {
							width: 47%;
							height: 580rpx;
							position: absolute;
							top: 162rpx;
							// left: -20rpx;
							display: flex;
							flex-direction: column;
							justify-content: space-between;
							.blogo_boxbody {
								width: 430rpx;
								height: 56rpx;
							}
							.bold_tit_box {
								width: 100%;
								height: auto;
								font-size: 95rpx;
								font-weight: bold;
								color: white;
								@media (max-width: 1650px) {
									font-size: 90rpx;
								}
								@media (max-width: 1550px) {
									font-size: 80rpx;
								}
								@media (max-width: 1450px) {
									font-size: 70rpx;
								}
								@media (max-width: 1300px) {
									font-size: 65rpx;
								}
							}
							.b3txt {
								width: 100%;
								height: auto;
								font-size: 40rpx;
								color: rgba(255, 255, 255, .8);
								// text-align: center;
								display: flex;
								align-items: center;
							}
						}
					}
				}

			}

		}
		.upgrades_boxbody {
			width: 100%;
			height: 1600rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			margin-bottom: 109px;
			.upgrades_box_body {
				width: 80%;
				height: 100%;
				// background-color: aqua;
				display: flex;
				flex-direction: column;
				padding: 84px 0;
				padding-right: 70px;
				padding-left: 70px;
				box-sizing: border-box;
				justify-content: space-between;
				@media (max-width: 1080px) {
					width: 99%;
				}
				.upgrades_bold_tit {
					width: 100%;
					height: auto;
					display: flex;
					flex-direction: column;
					align-items: center;
					justify-content: center;
					.u_bold_t {
						font-size: 110rpx;
						font-weight: bold;
						color: white;
						@media (max-width: 1650px) {
							font-size: 100rpx;
						}
						@media (max-width: 1550px) {
							font-size: 90rpx;
						}
						@media (max-width: 1450px) {
							font-size: 80rpx;
						}
						@media (max-width: 1300px) {
							font-size: 75rpx;
						}
					}
					.u_m_t {
						margin-top: 20rpx;
						font-size: 56rpx;
						// font-weight: bold;
						color: #BAFC51;
					}
				}
				.upgrades_img_mes_boxbody {
					width: 100%;
					height: 896rpx;
					display: flex;
					flex-direction: row;
					justify-content: space-between;
					align-items: center;
					position: relative;
					// background-color: aqua;
					.left_chose_boxbody {
						width: 380rpx;
						// flex: 1;
						height: 590rpx;
						// background-color: #8E8E92;
						border-right: 1rpx solid #4D4D4D;
						display: flex;
						flex-direction: column;
						justify-content: space-between;
						// padding-right: 68rpx;
						// box-sizing: border-box;
						.left_chose_box {
							flex: 1;
							font-size: 40rpx;
							text-align: right;
							color: #4D4D4D;
							padding-right: 68rpx;
							box-sizing: border-box;
							display: flex;
							align-items: center;
							justify-content: flex-end;
						}
						.left_chose_box.active {
							color: #BAFC51;
							border-right: 8rpx solid #BAFC51;
						}
					}
					.right_bo_mes_boxbody {
						width: 80%;
						height: 900rpx;
						// background-color: #8E8E92;
						display: flex;
						flex-direction: row;
						align-items: center;
						overflow: hidden;
						.bo_left_img_box {
							// flex: 2.5;
							width: 500rpx;
							height: 100%;
							// padding-left: 10px;
							// padding-right: 10px;
							// padding-top: 11px;
							// padding-bottom: 48px;
							// box-sizing: border-box;
							// background-image: url('https://pic1.imgdb.cn/item/6807549b58cb8da5c8bf54a6.png');
							// background-position: center;
							// background-repeat: no-repeat;
							// background-size: contain;

							.imggbox {
								width: 100%;
								height: 100%;
								// transition: transform .5s ease-in;
								// opacity: 0;
								// display: none;
								// animation-name: example1; /* 引用@keyframes定义的动画 */
								//   animation-duration: 2s; /* 动画持续时间 */
								//   animation-iteration-count: 1; /* 动画重复次数 */
								  
							}
							.imggbox.active {
								// opacity: 1;
								// transform: translateX(50px);
								display: block;
								animation-name: example; /* 引用@keyframes定义的动画 */
								  animation-duration: 2s; /* 动画持续时间 */
								  animation-iteration-count: 1; /* 动画重复次数 */
							}
						}
						.bo_right_mes_box_body {
							flex: 1;
							height: 100%;
							padding-top: 72rpx;
							padding-bottom: 140rpx;
							box-sizing: border-box;
							position: relative;
							// background-color: #1E1E1E;
							.mes_box_body {
								width: 100%;
								height: 100%;
								background-color: #1E1E1E;
								border-top-right-radius: 32rpx;
								border-bottom-right-radius: 32rpx;
								padding-left: 128rpx;
								padding-top: 58rpx;
								padding-bottom: 46rpx;
								padding-right: 30rpx;
								box-sizing: border-box;
								display: flex;
								flex-direction: column;
								justify-content: space-around;

								.mes_box_tit {
									font-size: 72rpx;
									color: white;
									// display: none;
								}
								.mes_box_tit.active {
									// opacity: 1;
									// transform: translateX(50px);
									display: block;
									animation-name: example; /* 引用@keyframes定义的动画 */
									  animation-duration: 2s; /* 动画持续时间 */
									  animation-iteration-count: 1; /* 动画重复次数 */
								}
								.mes_box_mes {
									font-size: 32rpx;
									color: white;
									line-height: 38rpx;
									// display: none;
								}
								.mes_box_mes.active {
									// opacity: 1;
									// transform: translateX(50px);
									display: block;
									animation-name: example; /* 引用@keyframes定义的动画 */
									  animation-duration: 2s; /* 动画持续时间 */
									  animation-iteration-count: 1; /* 动画重复次数 */
								}
							}

							.topview_bm_box_body {
								width: auto;
								height: auto;
								position: absolute;
								bottom: 0;
								right: 0;
								display: flex;
								flex-direction: row;
								align-items: center;
								justify-content: center;
								.topview_bm_box {
									width: 300rpx;
									height: 80rpx;
									margin-right: 60rpx;
									display: flex;
									flex-direction: row;
									align-items: center;
									justify-content: space-between;
									font-size: 36rpx;
									.tb {
										color: #4D4D4D;
									}
									.tb.active {
										color: #BAFC51;
									}
								}
								.anniu {
									width: 80rpx;
									height: 80rpx;
								}
							}
						}
					}
				}
			}
		}
		.funshot_box_body {
			width: 100%;
			height: 1600rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			margin-bottom: 200px;
			.funshot_boxbody {
				width: 66%;
				height: 100%;
				// background-color: aqua;
				display: flex;
				flex-direction: column;
				@media (max-width: 1650px) {
					width: 80%;
				}
				@media (max-width: 1080px) {
					width: 95%;
				}
				.funshot_toptit {
					width: 100%;
					height: auto;
					text-align: center;
					font-size: 112rpx;
					color: white;
					font-weight: bold;
					@media (max-width: 1650px) {
						font-size: 100rpx;
					}
					@media (max-width: 1550px) {
						font-size: 90rpx;
					}
					@media (max-width: 1450px) {
						font-size: 80rpx;
					}
					@media (max-width: 1300px) {
						font-size: 75rpx;
					}
					// @media (max-width: 1080px) {
					// 	font-size: 50rpx;
					// }
				}
				.funshot_bot_mesboxbody {
					flex: 1;
					padding-top: 20px;
					box-sizing: border-box;
					display: flex;
					flex-direction: row;
					align-items: center;
					justify-content: space-between;
					@keyframes example2 {
					  0% {color: #4D4D4D;}
					  50% {color: #b1b1b1;}
					  100% {color: white;}
					}
					@keyframes example3 {
					  0% {color: white;}
					  50% {color: #b1b1b1;}
					  100% {color: #4D4D4D;}
					}
					.funshotb_left {
						// background-color: #4D4D4D;
						font-size: 64rpx;
						color: #4D4D4D;
						flex: 1;
						height: 100%;
						display: flex;
						flex-direction: column;
						// justify-content: space-between;
						justify-content: center;
						@media (max-width: 1080px) {
							font-size: 50rpx;
						}
						.flf_box {
							width: 100%;
							height: auto;
							margin: 30rpx 0;
							.flf_b1 {
								width: 70%;
								height: auto;
								animation-name: example3; /* 引用@keyframes定义的动画 */
								  animation-duration: 2s; /* 动画持续时间 */
								  animation-iteration-count: 1; /* 动画重复次数 */
							}
							.flf_b2 {
								width: 95%;
								height: auto;
								display: flex;
								flex-direction: column;
								.flf_b2_tit {
									width: 100%;
									height: auto;
									font-size: 88rpx;
									color: white;
									animation-name: example2; /* 引用@keyframes定义的动画 */
									  animation-duration: 2s; /* 动画持续时间 */
									  animation-iteration-count: 1; /* 动画重复次数 */
									  @media (max-width: 1080px) {
									  	font-size: 70rpx;
									  }
								}
								.flf_b2_mes {
									width: 100%;
									height: auto;
									font-size: 44rpx;
									color: white;
									margin-top: 15px;
									animation-name: example2; /* 引用@keyframes定义的动画 */
									  animation-duration: 2s; /* 动画持续时间 */
									  animation-iteration-count: 1; /* 动画重复次数 */
									  @media (max-width: 1080px) {
									  	font-size: 30rpx;
									  }
								}
							}
						}
					}
					.funshotb_cen {
						// background-color: #BAFC51;
						// flex: 1;
						// height: 100%;
						width: 600rpx;
						height: 1120rpx;
						// padding-right: 40px;
						box-sizing: border-box;
					}
					@keyframes examplef {
					  0% {opacity: 0;}
					  50% {opacity: .5;}
					  100% {opacity: 1;}
					}
					@keyframes exampleff {
					  0% {opacity: 1;}
					  50% {opacity: .5;}
					  100% {opacity: 0;}
					}
					.funshotb_right {
						// background-color: #262626;
						flex: 1;
						height: 100%;
						display: flex;
						flex-direction: row;
						align-items: center;
						justify-content: center;
						// opacity: 0;
						animation-name: exampleff; /* 引用@keyframes定义的动画 */
						  animation-duration: 1s; /* 动画持续时间 */
						  animation-iteration-count: 1; /* 动画重复次数 */
						.num_box_body {
							width: auto;
							height: auto;
							display: flex;
							flex-direction: column;
							color: white;
							.num_box {
								font-size: 270rpx;
								font-weight: bolder;
								@media (max-width: 1080px) {
									font-size: 200rpx;
								}
							}
							.l_mes {
								font-size: 40rpx;
								color: rgba(255, 255, 255, .8);
								@media (max-width: 1080px) {
									font-size: 30rpx;
								}
							}
						}
						.dw_box {
							width: auto;
							height: auto;
							font-size: 72rpx;
							color: white;
							@media (max-width: 1080px) {
								font-size: 50rpx;
							}
						}
					}
					.funshotb_right.active {
						animation-name: examplef; /* 引用@keyframes定义的动画 */
						  animation-duration: 1s; /* 动画持续时间 */
						  animation-iteration-count: 1; /* 动画重复次数 */
					}
				}
			}
		}
		.aliexpress_box_body {
			width: 100%;
			height: 528rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			margin-bottom: 230px;
			.aliexpress_boxbody {
				width: 84%;
				height: 100%;
				background: linear-gradient(181.16deg, #E0FFAF -0.63%, #BAFC51 27.82%);
				border-radius: 32px;
				padding: 32px 0;
				padding-left: 200px;
				box-sizing: border-box;
				display: flex;
				flex-direction: row;
				@media (max-width: 1080px) {
					width: 95%;
					padding-left: 100px;
				}
				.left_ali_tab_boxbody {
					width: auto;
					height: 100%;
					display: flex;
					flex-direction: column;
					.ali_tab_boxbody {
						flex: 1;
						border-left: 2px solid rgba(255, 255, 255, .5);
						padding: 10px 0;
						padding-left: 25px;
						box-sizing: border-box;
						.alitabimg {
							width: auto;
							height: 46rpx;
						}
					}
					.ali_tab_boxbody.active {
						border-left-color: #000000;
					}
				}
				@keyframes example4 {
				  0% {opacity: 0;}
				  50% {opacity: .5;}
				  100% {opacity: 1;}
				}
				@keyframes example5 {
				  0% {opacity: 1;}
				  50% {opacity: .5;}
				  100% {opacity: 0;}
				}
				.right_ali_imgboxbody {
					width: auto;
					height: 100%;
					// display: none;
					// animation-name: example5; /* 引用@keyframes定义的动画 */
					//   animation-duration: 2s; /* 动画持续时间 */
					//   animation-iteration-count: 1; /* 动画重复次数 */
				}
				// .right_ali_imgboxbody.active {
				// 	display: block;
				// 	animation-name: example4; /* 引用@keyframes定义的动画 */
				// 	  animation-duration: 2s; /* 动画持续时间 */
				// 	  animation-iteration-count: 1; /* 动画重复次数 */
				// }
			}
		}
		.Full-chain {
			width: 100%;
			height: 1432rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			margin-bottom: 200px;
			.full_chain_bodbody {
				width: 62%;
				height: 100%;
				// background-color: aqua;
				display: flex;
				flex-direction: column;
				align-items: center;
				@media (max-width: 1650px) {
					width: 80%;
				}
				 @media (max-width: 1080px) {
				 	width: 95%;
				 } 
				.full_toptit {
					width: 100%;
					height: auto;
					font-size: 112rpx;
					color: white;
					font-weight: bold;
					margin-bottom: 49px;
					text-align: center;
				}
				.full_xz_boxbody {
					width: 79%;
					height: 136rpx;
					background-color: #252525;
					border-radius: 34px;
					display: flex;
					flex-direction: row;
					.xz_box_body {
						// flex: 1;
						width: 27%;
						height: 100%;
						display: flex;
						flex-direction: row;
						align-items: center;
						justify-content: space-between;
						.fullxz_an {
							width: 69%;
							height: 100%;
							display: flex;
							align-items: center;
							justify-content: center;
							color: white;
							font-size: 40rpx;
							text-align: center;
							padding: 0 30rpx;
						}
						.fullxz_an.active {
							background-color: #BAFC51;
							border-radius: 30px;
							color: #000000;
						}
						.youjiantou {
							width: 48rpx;
							height: 48rpx;
						}
					}
					.xz_box_body.xz1 {
						width: 19%;
						.fullxz_an {
							width: 100%;
						}
					}
				}
				.full_xz_botmes_boxbody {
					flex: 1;
					width: 100%;
					padding: 76px 0;
					padding-left: 100px;
					box-sizing: border-box;
					display: flex;
					flex-direction: row;
					align-items: center;
					.bot_leftimgbox {
						width: auto;
						height: 359px;
					}
					.bot_leftimgbox.lf1 {
						height: 236px;
					}
					.bot_rightmgbox {
						flex: 1;
						height: 100%;
						// background-color: aqua;
						padding-left: 60px;
						box-sizing: border-box;
						display: flex;
						flex-direction: column;
						align-items: center;
						justify-content: center;
						.tit1 {
							font-size: 40rpx;
							color: #BAFC51;
							width: 100%;
							height: auto;
						}
						.tit2 {
							font-size: 88rpx;
							color: #fff;
							width: 100%;
							height: auto;
						}
						.fmes {
							font-size: 32rpx;
							color: #fff;
							width: 100%;
							height: auto;
							line-height: 64rpx;
						}
						.learnmore_boxbody {
							width: 100%;
							height: auto;
							display: flex;
							flex-direction: row;
							align-items: center;
							justify-content: space-between;
							margin-top: 30px;
							.learnmore_box {
								/* Border */
								width: 150px;
								height: 48px;
								border: 2px solid #BAFC51;
								border-radius: 24px;
								color: #BAFC51;
								display: flex;
								align-items: center;
								justify-content: center;
								font-size: 32rpx;
							}
							.learnchange_boxbody {
								width: auto;
								height: auto;
								display: flex;
								flex-direction: row;
								align-items: center;
								justify-content: center;

								.learnchange_box {
									font-size: 36rpx;
									color: #4D4D4D;
									margin-right: 30px;
								}
								.learnchange_box.active {
									color: #BAFC51;
								}
							}
							.xiangyou {
								width: 80rpx;
								height: 80rpx;
							}
						}
					}
				}
			}
		}
		.Ultimate_Vision {
			width: 100%;
			height: 1600rpx;
			background-image: url('https://pic1.imgdb.cn/item/68056c9358cb8da5c8ba4228.png');
			background-position: center;
			background-repeat: no-repeat;
			background-size: contain;
			display: flex;
			flex-direction: column;
			margin-bottom: 150px;
			.vision_top_mesbox {
				width: 100%;
				height: auto;
				display: flex;
				flex-direction: column;
				align-items: center;
				.vt_toptit {
					width: 100%;
					height: auto;
					font-size: 112rpx;
					color: white;
					font-weight: bold;
					text-align: center;
				}
				.vt_botmes {
					width: 60%;
					margin: 20px 0;
					height: auto;
					font-size: 40rpx;
					color: rgba(255, 255, 255, .5);
					text-align: center;
				}
			}
			.vision_bot_swiperboxbody {
				width: 100%;
				flex: 1;
				display: flex;
				align-items: center;
				justify-content: center;
				.vision_bot_swiperbox {
					width: 59%;
					height: 100%;
					// background-color: aqua;
					display: flex;
					flex-direction: row;
					align-items: center;
					@media (max-width: 1080px) {
						width: 79%;
					} 
					.vi_limgbox {
						// width: 26%;
						// height: 100%;
						width: 600rpx;
						height: 1120rpx;
					}
					.vi_rswiperboxbody {
						width: 74%;
						height: 100%;
						padding: 50px 0;
						padding-right: 20px;
						box-sizing: border-box;
						.vi_rswiperbox {
							width: 97%;
							height: 100%;
							background-color: #000000;
							border-radius: 32rpx;
							padding: 40px;
							box-sizing: border-box;
							display: flex;
							flex-direction: column;
							.vi_toptit {
								font-size: 28px;
								color: white;
								width: 100%;
								height: auto;
								margin: 10px 0;
							}
							.vi_mes {
								width: 100%;
								height: auto;
								font-size: 16px;
								color: rgba(255, 255, 255, .8);
								margin: 10px 0;
							}
							.vi_mes1 {
								width: 100%;
								height: auto;
								font-size: 16px;
								color: rgba(255, 255, 255, .8);
								margin: 10px 0;
								display: flex;
								flex-direction: column;
								.mes1_mes {
									width: 100%;
									height: auto;
									display: flex;
									flex-direction: row;
									align-items: center;
									margin-bottom: 8px;
									.yuandian {
										width: 8px;
										height: 8px;
										background-color: #BAFC51;
										border-radius: 50%;
									}
									.mm {
										flex: 1;
										height: auto;
										margin-left: 5px;
									}
								}
							}
						}
					}
				}
			}
		}
		.revenue_model {
			width: 100%;
			height: 1630rpx;
			// min-height: 1230rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			margin-bottom: 236px;
			.revenue_model_boxbody {
				width: 62%;
				height: 100%;
				// background-color: aqua;
				display: flex;
				flex-direction: column;
				@media (max-width: 1650px) {
					width: 80%;
				}
				@media (max-width: 1080px) {
					width: 95%;
				}
				.re_top_tit {
					width: 100%;
					height: auto;
					font-size: 112rpx;
					font-weight: bold;
					color: white;
					text-align: center;
				}
				.re_top_mes {
					width: 100%;
					height: auto;
					font-size: 44rpx;
					// font-weight: bold;
					color: white;
					text-align: center;
					margin: 20px 0;
					margin-bottom: 0;
				}
				.diverse_boxbody {
					// flex: 1;
					width: 62%;
					position: relative;
					margin: 6.5rem auto 0;
					.bg {
						height: 480px;
					}
					.bg1 {
						height: 424px;
					}
					.svgbox {
						width: 100%;
						height: 100%;
						display: block;
						position: absolute;
						left: 0;
						top: 0;
						z-index: 5;
						#shopVoute {
						    width: 100%;
						    height: 100%;
						    display: block;
						    opacity: 0;
						    position: absolute;
						    left: 0;
						    top: 0;
						    z-index: 20;
						}
					}
					.pshop-nodes {
					    width: 100%;
					    height: 100%;
					    display: block;
					    position: absolute;
					    left: 0;
					    top: 0;
					    z-index: 30;
						color: white;
						.item1 {
						    height: calc(33% + 6rem);
						    right: 80%;
						    top: -5.5rem;
						    padding: 6rem 0 0 50vw;
							position: absolute;
							.txtbox {
								left: 50%;
								bottom: 100%;
								padding-bottom: 1.35rem;
								transform: translate(-50%, 0);
								-webkit-transform: translate(-50%, 0);
								position: absolute;
							}
						}
						.item2 {
						    height: calc(33% + 6rem);
						    right: 80%;
						    bottom: -5.5rem;
						    padding: 0 0 6rem 50vw;
							position: absolute;
							.txtbox {
								left: 50%;
								    top: 100%;
								    padding-top: 1.35rem;
								    transform: translate(-50%, 0);
								    -webkit-transform: translate(-50%, 0);
									position: absolute;
							}
						}
						.item3 {
						    height: calc(33% + 6rem);
						    left: 88%;
						    top: -5.5rem;
						    padding: 6rem 50vw 0 0;
							position: absolute;
							.txtbox {
							    left: 50%;
							    bottom: 100%;
							    padding-bottom: 1.35rem;
							    transform: translate(-50%, 0);
							    -webkit-transform: translate(-50%, 0);
								position: absolute;
							}
						}
						.item4 {
						    height: calc(33% + 6rem);
						    left: 88%;
						    bottom: -5.5rem;
						    padding: 0 50vw 6rem 0;
							position: absolute;
							.txtbox {
							    left: 50%;
							    top: 100%;
							    padding-top: 1.35rem;
							    transform: translate(-50%, 0);
							    -webkit-transform: translate(-50%, 0);
								position: absolute;
							}
						}
						.box {
							width: 100%;
							    height: 100%;
							    position: relative;
							    padding: 4px;
							    display: flex;
							    display: -webkit-flex;
							    justify-content: center;
							    -webkit-justify-content: center;
						}
						.tit {
							font-size: 24px;
							    line-height: 1.75rem;
							    font-weight: 700;
							    // white-space: nowrap;
								width: 370px;
								text-align: center;
						}
					}
				}
			}
		}
		.platform_boxbody {
			width: 100%;
			height: 1514px;
			display: flex;
			flex-direction: column;
			align-items: center;
			@media (max-width: 1080px) {
				height: 1314px;
			}
			.pla_top_mesbox {
				width: 55%;
				height: auto;
				font-size: 48px;
				font-weight: bold;
				color: white;
				text-align: center;
				margin-bottom: 110px;
				@media (max-width: 1080px) {
					width: 80%;
					font-size: 28px;
					margin-bottom: 80px;
				}
			}
			.pla_img_box {
				flex: 1;
				width: 67%;
				@media (max-width: 1080px) {
					width: 80%;
				}
			}
		}
		.partners_boxbody {
			width: 100%;
			height: 890px;
			display: flex;
			align-items: center;
			justify-content: center;
			margin-bottom: 150px;
			.partners_box {
				width: 75%;
				height: 100%;
				@media (max-width: 1280px) {
					width: 90%;
				}
			}
		}
		.team_boxbody {
			width: 100%;
			height: auto;
			display: flex;
			align-items: center;
			justify-content: center;
			.team_box_body {
				width: 67%;
				height: auto;
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;
				@media (max-width: 1660px) {
					width: 90%;
				}
				@media (max-width: 1280px) {
					width: 98%;
				}
				.t_top_tit {
					width: 100%;
					height: auto;
					font-size: 96rpx;
					font-weight: bold;
					color: white;
					text-align: center;
				}
				.t_top_mes {
					width: 66%;
					height: auto;
					font-size: 40rpx;
					color: rgba(255, 255, 255, .8);
					text-align: center;
					margin: 20px 0;
					margin-bottom: 40px;
				}

				.t_bot_tabbox_body {
					width: 100%;
					height: 393px;
					display: flex;
					flex-direction: row;
					justify-content: space-between;
					margin-bottom: 150px;
					@media (max-width: 1080px) {
						padding-bottom: 20px;
						box-sizing: border-box;
					}
					.t_tab_box {
						width: 23%;
						height: 100%;
						background-image: url('@/static/image/Group 1533213400.png');
						background-position: center;
						background-repeat: no-repeat;
						background-size: contain;
						display: flex;
						    flex-direction: column;
						    justify-content: space-between;
						    padding-bottom: 20px;
						    box-sizing: border-box;
							transition: 1s;
							@media (max-width: 1080px) {
								width: 24.5%;
							}
						.tab_top_userbox {
							width: 100%;
							height: 29%;
							// background-color: aqua;
							padding: 25px;
							box-sizing: border-box;
							display: flex;
							flex-direction: row;
							align-items: center;
							@media (max-width: 1080px) {
								 padding-top: 35px;
							}
							.userimg {
								height: 100%;
								aspect-ratio: 1 / 1;
								margin-right: 20px;
							}
							.usermes {
								flex: 1;
								height: 100%;
								display: flex;
								flex-direction: column;
								.name {
									flex: 1;
									color: white;
									font-size: 22px;
									display: flex;
									align-items: center;
								}
								.zw {
									flex: 1;
									color: #BABABA;
									font-size: 13px;
									display: flex;
									align-items: center;
								}
							}
						}
						.tab_mes_box {
							width: 100%;
							height: auto;
							padding: 0 15px;
							box-sizing: border-box;
							color: #999999;
							font-size: 13px;
							line-height: 26px;
							margin-bottom: 10px;
							@media (max-width: 1080px) {
								 font-size: 7px;
							}
						}
						.tab_keywords {
							width: 100%;
							height: auto;
							padding: 0 15px;
							box-sizing: border-box;
							color: #fff;
							font-size: 13px;
							line-height: 26px;
							margin-bottom: 10px;
							@media (max-width: 1080px) {
								 font-size: 7px;
							}
						}
						.tab_follower {
							width: 100%;
							height: auto;
							font-size: 13.5px;
							display: flex;
							flex-direction: row;
							padding: 0 15px;
							box-sizing: border-box;
							color: #999999;
							align-items: center;
							justify-content: space-between;
							@media (max-width: 1080px) {
								 font-size: 10px;
							}
							// margin-top: 10px;
						}
						.followerimg {
							width: 24px;
							height: 24px;
							@media (max-width: 1080px) {
								 width: 18px;
								 height: 18px;
							}
						}
					}
					.t_tab_box:hover {
						transform: scale(1.1, 1.1);
						transition: 1s;
					}
				}
			}
		}
		.bottom_box_body {
			width: 100%;
			height: 666px;
			display: flex;
			flex-direction: column;
			.top_concactus_boxbody {
				width: 100%;
				height: 387px;
				background-image: url('@/static/image/6805ddb858cb8da5c8bb655a.png');
				background-position: center;
				background-repeat: no-repeat;
				background-size: contain;
				position: relative;
				.concactus_box_body {
					width: 556px;
					height: 173px;
					display: flex;
					flex-direction: column;
					position: absolute;
					left: 15%;
					top: 20%;
					.c_toptit {
						font-size: 26px;
						color: white;
						margin-bottom: 8px;
					}
					.c_topmes {
						width: 100%;
						font-size: 16px;
						color: white;
						margin-bottom: 16px;
					}
					.c_an {
						width: 243px;
						height: 60px;
						display: flex;
						align-items: center;
						justify-content: center;
						border-radius: 60px;
						background-color: #BAFC51;
						color: #000000;
						font-size: 20px;
					}
				}
			}
			.bot_concactus_boxbody {
				flex: 1;
				width: 100%;
			}
		}
		.wordewide_boxbody {
			width: 100%;
			height: 602px;
			position: relative;
			.wordbanner_boxbody {
				width: 100%;
				height: 331px;
				background-image: url('https://pic1.imgdb.cn/item/680628a658cb8da5c8bcc4b7.png');
				background-position: center;
				background-repeat: no-repeat;
				background-size: contain;
			}
			.download_type_boxbody {
				width: 72%;
				height: 836px;
				position: absolute;
				top: 0;

				left: 18%;
				display: flex;
				flex-direction: row;
				align-items: center;
				.download_type_mesboxbody {
					width: 50%;
					height: 670px;
					// background-color: aqua;
					display: flex;
					flex-direction: column;

					.down_toptit {
						width: 100%;
						height: auto;
						font-size: 60px;
						font-weight: bold;
						color: white;
						margin-top: 20px;
					}
					.down_topmes {
						width: 100%;
						height: auto;
						font-size: 19px;
						color: rgba(255, 255, 255, 0.65);
						margin-bottom: 50px;
					}
					.down_typeboxbody {
						width: 100%;
						height: 56px;
						display: flex;
						flex-direction: row;
						justify-content: space-between;
					}
				}
				.qximg_boxbody {
					width: 332px;
					height: 670px;
					margin-left: 80px;
				}
			}
		}
		.ztbox_body {
			width: 100%;
			height: 1347px;
			background-color: white;
			padding-top: 105px;
			padding-bottom: 139px;
			padding-left: 290px;
			padding-right: 390px;
			box-sizing: border-box;
			display: flex;
			flex-direction: column;
			justify-content: space-between;
				@media (max-width: 1650px) {
					    padding-left: 150px;
					    padding-right: 150px;
				}
				
				@media (max-width: 1280px) {
					    padding-left: 50px;
					    padding-right: 50px;
				}
			.zt_topimg_box {
				width: 100%;
				height: auto;
				position: relative;
				.ztimgmes_boxbody {
					width: 48%;
					height: 524px;
					z-index: 99;
					right: 10%;
					top: 170px;
					position: absolute;
					padding: 135px 0;
					box-sizing: border-box;
					display: flex;
					flex-direction: column;
					justify-content: space-between;
					.zt1 {
						width: 100%;
						height: auto;
						font-size: 40px;
						font-weight: bold;
					}
					.zt2 {
						width: 100%;
						height: auto;
						font-size: 20px;
					}
					.zt3_boxbody {
						width: 100%;
						height: auto;
						display: flex;
						flex-direction: row;
						align-items: center;
						.zt3_box {
							width: auto;
							height: 100%;
							display: flex;
							flex-direction: column;
							justify-content: space-between;
							.zt3_t {
								font-size: 14px;
								font-weight: bold;
							}
							.zt3_b {
								font-size: 20px;
								font-weight: bold;
							}
						}
					}
				}
			}
			.zt_botimg_box {
				width: 100%;
				height: 316px;
				display: flex;
				flex-direction: row;
				justify-content: space-between;
				.botimgbox {
					width: 48%;
					height: 100%;
					position: relative;

					.ztbotimgmes {
						padding-top: 80px;
						box-sizing: border-box;
						display: flex;
						flex-direction: column;
						width: 100%;
						height: 100%;
						position: absolute;
						top: 0;
						z-index: 99;
						@media (max-width: 1080px) {
							padding-top: 30px;
						}
						.tm {
							font-size: 40px;
							font-weight: bold;
							text-align: center;
							@media (max-width: 1080px) {
								font-size: 30px;
							}
						}
						.tm1 {
							font-size: 20px;
							padding: 44px 0;
							box-sizing: border-box;
							text-align: center;
						}
					}
				}
			}
		}
		.dw_bottom {
			width: 100%;
			height: 323px;
		}
		.joinus_boxbody {
			width: 100%;
			height: 1027px;
			// padding-top: 102px;
			margin-top: 202px;
			// box-sizing: border-box;
			display: flex;
			flex-direction: row;
			.j_leftimgbox {
				flex: 1;
				height: 100%;
				padding-top: 60px;
				box-sizing: border-box;
				display: flex;
				flex-direction: column;
				align-items: center;
				.imgjbox {
					width: 300px;
					height: 560px;
					margin-bottom: 43px;
				}
				.j_biconbox {
		width: 100%;
		height: auto;
		display: flex;
		flex-direction: row;
		align-items: center;
		justify-content: center;
					.j_biconb {
						width: auto;
						height: auto;
						display: flex;
						flex-direction: column;
						margin-right: 70px;
						.biconbox {
							width: auto;
							height: 25.5px;
						}
						.j_btit1 {
							margin-top: 17px;
							margin-bottom: 4px;
							font-size: 16px;
							color: #6C6C70;
						}
						.j_btit2 {
							font-size: 19px;
							color: #F0F0F0;
						}
					}
				}
			}
			.right_form_boxbody {
				width: 63%;
				height: 100%;
				background-color: white;
				border-top-left-radius: 40px;
				padding-top: 60px;
				padding-left: 76px;
				padding-right: 330px;
				box-sizing: border-box;
				display: flex;
				flex-direction: column;
				@media (max-width: 1080px) {
					padding-right: 180px;
				}
				.joinimgbox {
					width: 100%;
					height: 310rpx;
					margin-bottom: 35px;
					display: flex;
					flex-direction: column;
					.joinust {
						font-size: 112rpx;
						font-weight: bold;
						margin-bottom: 32rpx;
					}
					.joinmes {
						font-size: 40rpx;
					}
				}
				.xz_boxx_body {
					width: 100%;
					height: auto;
					display: flex;
					flex-direction: row;
					.xzlefttit {
						height: 25px;
						width: auto;
						font-size: 36rpx;
					}
					.xzrightbox {
						flex: 1;
						padding-left: 80px;
						box-sizing: border-box;
					}
				}
				.joinbtn {
					/* Background+Border */
					
					box-sizing: border-box;
					
					// position: absolute;
					width: 128px;
					height: 48px;
					// left: 60px;
					// top: 636px;
					
					background: #BAFC51;
					border: 2px solid #BAFC51;
					border-radius: 24px;
					display: flex;
					align-items: center;
					justify-content: center;
				}
			}
		}
		.bottom_joinbo {
			width: 100%;
			height: 323px;
			background-color: #000000;
			padding: 0 654rpx;
			padding-top: 94rpx;
			box-sizing: border-box;
			display: flex;
			flex-direction: row;
			@media (max-width: 1650px) {
				padding: 0 200rpx;
				padding-top: 94rpx;
			}
			@media (max-width: 1080px) {
				padding: 0 80rpx;
				padding-top: 94rpx;
			}
			.dh_menu_boxbody {
				display: flex;
				flex-direction: column;
				width: auto;
				height: auto;
				margin-right: 42px;
				.dhtit1 {
					width: auto;
					height: auto;
					font-size: 18px;
					color: white;
					opacity: .7;
					margin-bottom: 32px;
				}
				.dhtit2 {
					width: auto;
					height: auto;
					font-size: 16px;
					color: white;
					margin-bottom: 20px;
				}
				.dhiconboxbody {
					width: auto;
					height: auto;
					display: flex;
					flex-direction: row;
					.dhicon {
						width: 32px;
						height: 32px;
					}
				}
			}
			.Subscribe_boxbody {
				width: auto;
				height: auto;
				display: flex;
				flex-direction: column;
				margin-left: 120px;
				.Subscribe_topbox {
					width: 480px;
					height: 60px;
					border-radius: 30px;
					background-color: #1A1A1A;
					padding: 19px 34px;
					box-sizing: border-box;
					position: relative;
					color: #757575;
					.sub_box {
						width: 104px;
						height: 60px;
						background-color: white;
						border-radius: 30px;
						position: absolute;
						top: 0;
						right: 0;
						font-size: 16px;
						display: flex;
						align-items: center;
						justify-content: center;
						color: #000000;
					}
				}
				.subtit {
					width: 480px;
					height: auto;
					font-size: 14px;
					color: white;
					margin-top: 23px;
				}
			}
		}
	}
// PC端样式
.pc {
	
	.hmban-mouse-wrap {
	    width: 100%;
	    position: absolute;
	    left: 0;
	    bottom: 0;
	    z-index: 10;
	    pointer-events: none;
	    display: flex;
	    justify-content: center;
		.hmban-mouse {
			width: 24px;
			    height: 32px;
			    display: block;
			    margin: 0 auto;
			    position: relative;
				.dw {
					width: 24px;
					    height: 15px;
					    display: block;
					    background: url('https://www.tiktokforbusinessoutbound.com/img/static/home-mouse-white.svg') no-repeat center center;
					    background-size: contain;
					    position: absolute;
					    left: 0;
					    top: 0;
					    z-index: 1;
					    opacity: 0;
				}
				@keyframes mouseflv {
				  0% {opacity: 0;top: 0;}
				  50% {opacity: 1;top: 10%;}
				  100% {opacity: 0;top: 100%;}
				}
				@keyframes mouseflv2 {
				  0% {opacity: 0;top: 0;}
				  50% {opacity: 1;top: 20%;}
				  100% {opacity: 0;top: 100%;}
				}
				.d1 {
					animation: mouseflv 2s linear infinite both;
				}
				.d2 {
					opacity: 0;
					animation: mouseflv2 2s 1s linear infinite both;
				}
		}
	}
	
	// .joinus_boxbody {
	// 	width: 100%;
	// 	height: 1027px;
	// 	// padding-top: 102px;
	// 	margin-top: 202px;
	// 	// box-sizing: border-box;
	// 	display: flex;
	// 	flex-direction: row;
	// 	.j_leftimgbox {
	// 		flex: 1;
	// 		height: 100%;
	// 		padding-top: 60px;
	// 		box-sizing: border-box;
	// 		display: flex;
	// 		flex-direction: column;
	// 		align-items: center;
	// 		.imgjbox {
	// 			width: 300px;
	// 			height: 560px;
	// 			margin-bottom: 43px;
	// 		}
	// 		.j_biconbox {
 //    width: 100%;
 //    height: auto;
 //    display: flex;
 //    flex-direction: row;
 //    align-items: center;
 //    justify-content: center;
	// 			.j_biconb {
	// 				width: auto;
	// 				height: auto;
	// 				display: flex;
	// 				flex-direction: column;
	// 				margin-right: 70px;
	// 				.biconbox {
	// 					width: auto;
	// 					height: 25.5px;
	// 				}
	// 				.j_btit1 {
	// 					margin-top: 17px;
	// 					margin-bottom: 4px;
	// 					font-size: 16px;
	// 					color: #6C6C70;
	// 				}
	// 				.j_btit2 {
	// 					font-size: 19px;
	// 					color: #F0F0F0;
	// 				}
	// 			}
	// 		}
	// 	}
	// 	.right_form_boxbody {
	// 		width: 63%;
	// 		height: 100%;
	// 		background-color: white;
	// 		border-top-left-radius: 40px;
	// 		padding-top: 60px;
	// 		padding-left: 76px;
	// 		padding-right: 330px;
	// 		box-sizing: border-box;
	// 		display: flex;
	// 		flex-direction: column;
	// 		.joinimgbox {
	// 			width: 100%;
	// 			height: 310rpx;
	// 			margin-bottom: 35px;
	// 			display: flex;
	// 			flex-direction: column;
	// 			.joinust {
	// 				font-size: 112rpx;
	// 				font-weight: bold;
	// 				margin-bottom: 32rpx;
	// 			}
	// 			.joinmes {
	// 				font-size: 40rpx;
	// 			}
	// 		}
	// 		.xz_boxx_body {
	// 			width: 100%;
	// 			height: auto;
	// 			display: flex;
	// 			flex-direction: row;
	// 			.xzlefttit {
	// 				height: 25px;
	// 				width: auto;
	// 				font-size: 36rpx;
	// 			}
	// 			.xzrightbox {
	// 				flex: 1;
	// 				padding-left: 80px;
	// 				box-sizing: border-box;
	// 			}
	// 		}
	// 		.joinbtn {
	// 			/* Background+Border */
				
	// 			box-sizing: border-box;
				
	// 			// position: absolute;
	// 			width: 128px;
	// 			height: 48px;
	// 			// left: 60px;
	// 			// top: 636px;
				
	// 			background: #BAFC51;
	// 			border: 2px solid #BAFC51;
	// 			border-radius: 24px;
	// 			display: flex;
	// 			align-items: center;
	// 			justify-content: center;
	// 		}
	// 	}
	// }
	// .bottom_joinbo {
	// 	width: 100%;
	// 	height: 323px;
	// 	background-color: #000000;
	// 	padding: 0 654rpx;
	// 	padding-top: 94rpx;
	// 	box-sizing: border-box;
	// 	display: flex;
	// 	flex-direction: row;
	// 	.dh_menu_boxbody {
	// 		display: flex;
	// 		flex-direction: column;
	// 		width: auto;
	// 		height: auto;
	// 		margin-right: 42px;
	// 		.dhtit1 {
	// 			width: auto;
	// 			height: auto;
	// 			font-size: 18px;
	// 			color: white;
	// 			opacity: .7;
	// 			margin-bottom: 32px;
	// 		}
	// 		.dhtit2 {
	// 			width: auto;
	// 			height: auto;
	// 			font-size: 16px;
	// 			color: white;
	// 			margin-bottom: 20px;
	// 		}
	// 		.dhiconboxbody {
	// 			width: auto;
	// 			height: auto;
	// 			display: flex;
	// 			flex-direction: row;
	// 			.dhicon {
	// 				width: 32px;
	// 				height: 32px;
	// 			}
	// 		}
	// 	}
	// 	.Subscribe_boxbody {
	// 		width: auto;
	// 		height: auto;
	// 		display: flex;
	// 		flex-direction: column;
	// 		margin-left: 120px;
	// 		.Subscribe_topbox {
	// 			width: 480px;
	// 			height: 60px;
	// 			border-radius: 30px;
	// 			background-color: #1A1A1A;
	// 			padding: 19px 34px;
	// 			box-sizing: border-box;
	// 			position: relative;
	// 			color: #757575;
	// 			.sub_box {
	// 				width: 104px;
	// 				height: 60px;
	// 				background-color: white;
	// 				border-radius: 30px;
	// 				position: absolute;
	// 				top: 0;
	// 				right: 0;
	// 				font-size: 16px;
	// 				display: flex;
	// 				align-items: center;
	// 				justify-content: center;
	// 				color: #000000;
	// 			}
	// 		}
	// 		.subtit {
	// 			width: 480px;
	// 			height: auto;
	// 			font-size: 14px;
	// 			color: white;
	// 			margin-top: 23px;
	// 		}
	// 	}
	// }
	
	.mstValue1-imgsbox {
	    width: 75vw;
	    max-width: 100%;
	    height: calc(100% - 3.5rem);
	    /* height: calc(100vh - 80px - 9.8rem); */
	    margin: 0 auto;
	    position: relative;
	    z-index: 10;
	    top: -5.5rem;
		@media (max-width: 1080px) {
			top: 0;
		}
		.mstValue1-imgs {
			width: 100%;
			    height: 100%;
			    margin: 0 auto;
			    overflow: hidden;
			    position: relative;
			    z-index: 10;
				.row {
					margin: 0 -2rem;
					display: flex;
					display: -webkit-flex;
					position: relative;
					height: 100%;
					.imgslist {
						width: 25%;
						padding: 0 2rem;
						text-align: center;
						position: relative;
						transition: all .4s cubic-bezier(0.33, 1, 0.68, 1);
						-webkit-transition: all .4s cubic-bezier(0.33, 1, 0.68, 1);
					}
					.list1 {
						margin-top: 12.15rem;
						    /* top: 733px; */
						transform: translateY(37.7%);
						-webkit-transform: translateY(37.7%);
					}
				}
				li {
					margin-bottom: 2.5rem;
				}
				.item-tit {
					width: 100%;
					text-align: center;
					overflow: hidden;
					margin-bottom: 2.5rem;
					font-size: 2rem;
					line-height: 1.45em;
					color: #fff;
					font-weight: 700;
					@media (max-width: 1080px) {
						font-size: 18px;
					}
				}
		}
	}
	.mstValue1-imgsbox::before {
	    content: '';
	    display: block;
	    width: 100%;
	    height: 14rem;
	    background: url('https://www.tiktokforbusinessoutbound.com/img/market/black-top.svg') no-repeat center bottom;
	    background-size: cover;
	    position: absolute;
	    left: 0;
	    top: -2rem;
	    z-index: 20;
	}

}


// 手机端样式
.mobile {
	
	.app_bot_dhboxbody {
		width: 100%;
		height: 100%;
		padding: 40rpx 30rpx;
		// padding-top: 40rpx;
		box-sizing: border-box;
		border-top: 2rpx solid #757575;
		border-bottom: 2rpx solid #757575;
		display: flex;
		flex-direction: column;
		.logoboxbody {
			width: 100%;
			height: auto;
			display: flex;
			flex-direction: row;
			align-items: center;
			.logobox {
				width: 60rpx;
				height: 60rpx;
				overflow: hidden;
				margin-right: 20rpx;
			}
			.logotit_box {
				width: auto;
				height: 100%;
				display: flex;
				flex-direction: column;
				justify-content: space-between;
				.toptit {
					color: #F4F5F7;
					font-size: 22rpx;
				}
				.bottit {
					color: #8E8E92;
					font-size: 20rpx;
				}
			}
		}
		.dhboxbody {
			width: 100%;
			height: auto;
			margin: 30rpx 0;
			margin-bottom: 15rpx;
			display: flex;
			flex-direction: column;
			.ttit {
				color: #BAFC51;
				font-size: 30rpx;
				font-weight: bold;
			}
			.ctit {
				color: rgba(255, 255, 255, .43);
				font-size: 26rpx;
				margin: 30rpx 0;
			}
		}
	}
	
	
	.partners_box {
		width: 672rpx!important;
	}
	.upgrades_boxbody {
		margin: 0;
	}
	.funshot_box_body,.aliexpress_box_body,.Full-chain,.Ultimate_Vision {
		margin-bottom: 100rpx;
	}
	.partners_boxbody {
		height: auto;
		margin-bottom: 100rpx;
	}
	.alitabimg {
		width: auto;
		height: 18rpx;
	}
	.content {
		display: flex;
		flex-direction: column;
		align-items: center;
		/* justify-content: center; */
	}
	.index_body {
		width: 750rpx;
		height: auto;
		min-height: 300rpx;
		background-color: #000000;
		padding-top: 0!important;
		box-sizing: border-box;
		.appheader {
			width: 100%;
			height: 122rpx;
			border-bottom: 1rpx solid #262626;
			padding: 24rpx;
			padding-right: 18rpx;
			box-sizing: border-box;
			display: flex;
			flex-direction: row;
			justify-content: space-between;
			position: fixed;
			    top: 0;
			    z-index: 999;
			    background-color: #000000;
			.left_apphaderlogo {
				height: 100%;
				width: 282rpx;
				display: flex;
				flex-direction: row;
				align-items: center;
				// justify-content: space-between;
				padding: 9.6rpx;
				box-sizing: border-box;
				.logo_appbox {
					height: 100%;
					aspect-ratio: 1 / 1;
					border-radius: 15rpx;
					overflow: hidden;
					margin-right: 20rpx;
				}
				.appname_box {
					width: auto;
					height: 100%;
					display: flex;
					flex-direction: column;
					.appname_t {
						width: auto;
						height: auto;
						font-size: 22rpx;
						font-weight: bold;
						color: white;
					}
					.appname_b {
						color: #8E8E92;
						font-size: 16rpx;
					}
				}
			}
			.hotapp_box {
				width: 58rpx;
				height: 58rpx;
			}
		}
		.pc_banner_boxbody {
			height: 940rpx;
			.spswiper {
			    width: 100%;
			    height: 100%;
			    display: flex;
			    flex-direction: column;
			    align-items: center;
				.top_tit_mes_box {
					width: 100%;
					padding: 0 80rpx;
					box-sizing: border-box;
					height: auto;
					.top_tit_mes {
						width: 100%;
						.top_tit {
							font-size: 52rpx;
							text-align: center;
							white-space: normal;
						}
						.mes_box {
							font-size: 24rpx;
						}
					}
				}
				// .banner_swiper_boxbody {
				// 	height: 400rpx;
				// }
			}
		}
		.upgrades_boxbody {
			height: auto;
			.upgrades_box_body {
				width: 100%;
				padding: 0;
				.u_bold_t {
					font-size: 52rpx;
					text-align: center;
				}
				.u_m_t {
					font-size: 24rpx;
					margin: 30rpx 0;
				}
				.upgrades_img_mes_boxbody {
					height: auto;
					flex-direction: column;
					.left_chose_boxbody {
						margin: 30rpx 0;
						flex-direction: row;
						width: 100%;
						height: auto;
						padding: 0 22rpx;
						box-sizing: border-box;
						.left_chose_box {
							font-size: 18rpx;
							padding-right: 0;
							padding-bottom: 28rpx;
							border-right: 0;
							text-align: center;
							border-bottom: 2rpx solid #4D4D4D;
							display: flex;
							justify-content: center;
						}
						.left_chose_box.active {
							color: #BAFC51;
							border-bottom: 4rpx solid #BAFC51;
						}
					}
					.right_bo_mes_boxbody {
						width: 100%;
						height: auto;
						padding: 0 22rpx;
						box-sizing: border-box;
						flex-direction: column;
						align-items: center;
						overflow: hidden;
						.bo_left_img_box {
							width: 260rpx;
							height: 472rpx;
						}
						.bo_right_mes_box_body {
							.mes_box_body {
								width: 100%;
								padding: 0;
								background-color: transparent;
								.mes_box_tit {
									font-size: 34rpx;
									text-align: center;
								}
								.mes_box_mes {
									font-size: 22rpx;
									text-align: center;
								}
							}
							.topview_bm_box_body {
								margin-right: 60rpx;
								.topview_bm_box {
									font-size: 26rpx;
								}
								.anniu {
									width: 42rpx;
									height: 42rpx;
								}
							}

						}
					}
				}

			}
		}
		.funshot_box_body {
			height: auto;
			.funshot_boxbody {
				width: 100%;
				padding: 0 22rpx;
				box-sizing: border-box;
				.funshot_toptit {
					font-size: 52rpx;
				}
				.funshot_bot_mesboxbody {
					.funshotb_left {
						font-size: 28rpx;
						.flf_box {
							margin: 20rpx 0;
						}
						.flf_b2 {
							.flf_b2_tit {
								font-size: 38rpx;
							}
							.flf_b2_mes {
								font-size: 22rpx;
							}
						}
					}
					.app_funshotb_boxbody {
						width: 260rpx;
						height: auto;
						display: flex;
						flex-direction: column;
						.app_fbimgbox {
							width: 100%;
							height: 485rpx;
							margin-bottom: 28rpx;
						}
						.app_fbsjbox {
							width: 100%;
							height: auto;
							display: flex;
							flex-direction: row;
							align-items: center;
							.to_fbsjb {
								width: 184rpx;
								height: auto;
								display: flex;
								flex-direction: column;
								color: white;
								.l_fbsjb {
									font-size: 82rpx;
								}
								.l_fbsmes {
									font-size: 12rpx;
									color: rgba(255, 255, 255, .8);
								}
							}
							.fh {
								font-size: 20rpx;
								color: white;
							}
						}
					}
				}
			}
		}
		.aliexpress_box_body {
			height: 482rpx;
			padding: 0 14rpx;
			box-sizing: border-box;
			.aliexpress_boxbody {
				width: 100%;
				padding: 30rpx 40rpx;
				box-sizing: border-box;
				border-radius: 48rpx;
				flex-direction: column;
				overflow: hidden;
				.left_ali_tab_boxbody {
					width: 100%;
					height: auto;
					flex-direction: row;
					.ali_tab_boxbody {
						border-top: 2px solid rgba(255, 255, 255, 0.5);
						    padding: 28rpx 0;
						    // padding-left: 25px;
						    box-sizing: border-box;
							border-left: 0;
							padding-left: 0;
							text-align: center;
					}
					.ali_tab_boxbody.active {
						border-top-color: #000000;
					}
				}
			}
		}
		.Full-chain {
			height: auto;
			padding: 0 22rpx;
			box-sizing: border-box;
			.full_chain_bodbody {
				width: 100%;
				.full_toptit {
					font-size: 54rpx;
				}
				.full_xz_boxbody {
					width: 100%;
					height: 77rpx;
					padding: 8rpx;
					.xz_box_body {
						.fullxz_an {
							font-size: 21rpx;
						}
					}
				}
				.full_xz_botmes_boxbody {
					flex-direction: column;
					padding: 0;
					margin-top: 46rpx;
					.bot_leftimgbox {
						width: 363rpx;
						height: 576rpx;
					}
					.bot_rightmgbox {
						padding: 0;
						margin-top: 30rpx;
						.tit1 {
							font-size: 23rpx;
							text-align: center;
						}
						.tit2 {
							font-size: 43rpx;
							text-align: center;
						}
						.fmes {
							font-size: 23rpx;
							text-align: center;
							line-height: 33rpx;
						}
						.learnmore_boxbody {
							align-items: center;
							justify-content: center;
						}
					}
				}
			}
		}
		.Ultimate_Vision {
			height: auto;
			background-image: url('https://pic1.imgdb.cn/item/680610c658cb8da5c8bc53b4.png');
			.vi_rswiperbox {
				padding: 38rpx!important;
				height: auto !important;
			}
			.vision_top_mesbox {
				.vt_toptit {
					font-size: 50rpx;
				}
				.vt_botmes {
					font-size: 23rpx;
				}
			}
			.vision_bot_swiperboxbody {
				// flex-direction: column;
				.vision_bot_swiperbox {
					flex-direction: column;
					width: 100%;
					padding: 0 40rpx;
					box-sizing: border-box;
					align-items: center;
					.vi_limgbox {
						width: 270rpx;
						height: 504rpx;
					}
					.vi_rswiperboxbody {
						width: 100%;
						height: 1480rpx;
						padding-right: 0;
					}
				}
			}
		}
		.revenue_model {
			height: auto;
			padding: 0 22rpx;
			box-sizing: border-box;
			margin-bottom: 100rpx;
			.revenue_model_boxbody {
				width: 100%;
				.re_top_tit {
					font-size: 48rpx;
				}
				.re_top_mes {
					font-size: 23rpx;
				}
				.diverse_boxbody {
					margin-top: 40rpx;
					.bg {
						height: 300rpx;
					}

				}
				.app_dri_boxbody {
					width: 100%;
					height: auto;
					display: flex;
					flex-direction: column;
					margin-top: 30rpx;
					.app_dri_box {
						width: 100%;
						height: auto;
						display: flex;
						flex-direction: column;
						margin-bottom: 25rpx;
						.dritit {
							font-size: 23rpx;
							color: white;
						}
						.drimes {
							margin-top: 10rpx;
							font-size: 23rpx;
							color: rgba(255, 255, 255, 0.8);
						}
					}
				}
			}
		}
		.platform_boxbody {
			height: auto;
			padding: 0 22rpx;
			box-sizing: border-box;
			.pla_top_mesbox {
				width: 100%;
				font-size: 46rpx;
			}
			.app_img_box {
				width: 100%;
				height: auto;
				display: flex;
				flex-direction: row;
				.leftimgbox {
					width: 50%;
					height: 1200rpx;
				}
				.rightimgbox {
					width: 50%;
					height: 1200rpx;
					margin-top: 50rpx;
				}
			}
		}
		.team_boxbody {
			.team_box_body{
				width: 100%;
				.t_top_tit {
					font-size: 48rpx;
				}
				.t_top_mes {
					font-size: 20rpx;
				}
				.t_bot_tabbox_body {
					height: 672rpx;
					.t_tab_box {
						width: 500rpx;
						height: 100%;
						// display: inline-block;
						margin-left: 40rpx;
						.tab_mes_box {
							white-space: normal;
							font-size: 22rpx;
						}
						.tab_keywords {
							white-space: normal;
							font-size: 22rpx;
						}
					}
				}
			}
		}
		.bottom_box_body {
			width: 100%;
			height: 1024rpx;
		}
	}
	.down_body {
		.hotapp_box {
			height: 64rpx!important;
			width: auto!important;
			min-width: 172rpx!important;
			border: 1px solid #424242;
			border-radius: 16rpx;
			padding: 10rpx;
			box-sizing: border-box;
			display: flex;
			flex-direction: row;
			align-items: center;
			.backimg {
				width: 30rpx;
				height: 30rpx;
			}
			.backmes {
				font-size: 28rpx;
				color: white;
				width: auto;
				margin-right: 10rpx;
			}
		}
		.app_topmes_boxbody {
			width: 100%;
			height: auto;
			display: flex;
			flex-direction: column;
			margin-bottom: 30rpx;
			.app_tbannerbox {
				width: 100%;
				height: auto;
			}
			.app_dtoptit {
				width: 100%;
				height: auto;
				font-size: 56rpx;
				font-weight: bold;
				color: white;
				text-align: center;
			}
			.app_dtopmes {
				width: 100%;
				height: auto;
				font-size: 28rpx;
				padding: 0 46rpx;
				box-sizing: border-box;
				color: rgba(255, 255, 255, 0.65);
				text-align: center;
			}
		}
		.app_dcen_imgbox {
			width: 100%;
			height: auto;
			padding: 0 94rpx;
			padding-left: 54rpx;
			box-sizing: border-box;
		}
		.app_dbotboxbody {
			width: 100%;
			height: auto;
			position: relative;
			background-color: white;
			// top: -300rpx;
			.app_dfristboxbody {
				width: 100%;
				min-height: 1920rpx;
				height: auto;
				margin-bottom: 300rpx;
				background-color: white;
				display: flex;
				flex-direction: column;
				// margin-bottom: 80rpx;
				.app_ddowntyebox {
					width: 100%;
					height: 130rpx;
					display: flex;
					flex-direction: row;
					justify-content: space-between;
					padding: 20rpx 50rpx;
					box-sizing: border-box;
					position: fixed;
					    bottom: 0;
					    z-index: 999;
					    background-color: white;
				}
				.app_ddimgbox {
					width: 100%;
					min-height: 1440rpx;
					height: auto;
					padding: 0 44rpx;
					box-sizing: border-box;
					margin-top: 25rpx;
					position: relative;
					background-image: url('https://pic1.imgdb.cn/item/680af5ce58cb8da5c8c9ce91.png');
					background-position: center;
					background-repeat: no-repeat;
					background-size: contain;
					.appddimgmes_boxbody {
						width: 100%;
						height: auto;
						padding-top: 76rpx;
						padding-left: 30rpx;
						padding-right: 30rpx;
						padding-bottom: 30rpx;
						box-sizing: border-box;
						display: flex;
						flex-direction: column;
						.tt1 {
							font-size: 40rpx;
							font-weight: bold;
							text-align: center;
						}
						.tt2 {
							font-size: 26rpx;
							// font-weight: bold;
							text-align: center;
							margin: 20rpx 0;
						}
						.tt3boxbody {
							width: 100%;
							height: 100rpx;
							display: flex;
							flex-direction: row;
							align-items: center;
							.zt3_box {
								width: 170rpx;
								height: auto;
								display: flex;
								flex-direction: column;
								justify-content: space-between;
								.zt3_t {
									font-size: 18rpx;
									font-weight: bold;
								}
								.zt3_b {
									font-size: 26rpx;
									font-weight: bold;
								}
							}
						}
					}
				}
	
			}
			.app_ddimgbox1 {
				width: 100%;
				height: auto;
				padding: 0 44rpx;
				box-sizing: border-box;
				margin-top: 25rpx;
				background-color: white;
				margin-bottom: 80rpx;
				position: relative;
				.ddimgbox {
					width: 100%;
					height: 494rpx;
					margin-bottom: 25rpx;
					position: relative;
					.ddimgboxmes_body {
						width: 100%;
						height: 100%;
						position: absolute;
						z-index: 99;
						top: 0;
						padding-top: 102rpx;
						padding-left: 14rpx;
						padding-right: 14rpx;
						box-sizing: border-box;
						display: flex;
						flex-direction: column;
						.tm {
							font-size: 46rpx;
							font-weight: bold;
							text-align: center;
						}
						.tm1 {
							font-size: 26rpx;
							// font-weight: bold;
							text-align: center;
							margin-top: 30rpx;
						}
					}
				}
			}
			.bottom_appdbot {
				width: 100%;
				height: auto;
			}
		}
}
}

