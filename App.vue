<script>
	export default {
		onLaunch: function() {
			// console.log('App Launch')
			// const ua = navigator.userAgent;
			// const isPC = /Windows NT|Macintosh/.test(ua); // 更复杂的判断可以根据需要添加更多条件
			// if (isPC) {
			// 	document.body.classList.add('pc');
			// 	uni.setStorageSync('ispc', true)
			// } else {
			// 	document.body.classList.add('mobile');
			// 	uni.setStorageSync('ispc', false)
			// }
			// 初始化时检查宽度并设置class
			// this.checkAndSetBodyClass();
			// 添加窗口大小变化的事件监听
			// window.addEventListener('resize', this.checkAndSetBodyClass);
			uni.preloadPage({
				url: '/pages/join/index'
			})
			// uni.preloadPage({
			// 	url: '/pages/download/index'
			// })
		},
		onShow: function() {
			console.log('App Show')
		},
		onHide: function() {
			console.log('App Hide')
			// 页面隐藏时移除事件监听器，避免内存泄漏
			// window.removeEventListener('resize', this.checkAndSetBodyClass);
		},
		methods: {
			checkAndSetBodyClass() {
				const width = window.innerWidth;
				const body = document.body;

				// 移除已有的类，然后根据需要添加新的类
				body.className = ''; // 清空所有class
				if (width < 960) {
					body.classList.add('mobile');
					uni.setStorageSync('ispc', false)
				} else {
					body.classList.add('pc');
					uni.setStorageSync('ispc', true)
				}
			}
		}

	}
</script>

<style>
	/*每个页面公共css */
	@import 'static/css/iconfont.css';
</style>