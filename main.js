import App from './App'

// #ifndef VUE3
import Vue from 'vue'
import i18n from './i18n.js'; // 引入国际化配置文件
import './uni.promisify.adaptor'
import '@babel/polyfill';
Vue.config.productionTip = false
App.mpType = 'app'
const app = new Vue({
	...App
})
app.use(i18n)
app.$mount()
// #endif

// #ifdef VUE3
import {
	createSSRApp
} from 'vue'
import i18n from './i18n.js'; // 引入国际化配置文件
export function createApp() {
	const app = createSSRApp(App)
	app.use(i18n)
	return {
		app
	}
}
// #endif