<!DOCTYPE html>
<html lang="en">
	<head>
		<meta charset="UTF-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0">
		<!-- HTML Meta Tags -->
		<title>FunShot - Kebahagiaan yang tidak diketahui orang lain</title>
		<meta name="description"
			content="Unduh FunShot, platform drama pendek dengan Neymar sebagai duta global. Tonton & hasilkan F-Bean sekarang!">

		<!-- Facebook Meta Tags -->
		<meta property="og:url" content="https://f-s.fun/">
		<meta property="og:type" content="website">
		<meta property="og:title" content="FunShot - Kebahagiaan yang tidak diketahui orang lain">
		<meta property="og:description"
			content="Unduh FunShot, platform drama pendek dengan Neymar sebagai duta global. Tonton & hasilkan F-Bean sekarang!">
		<meta property="og:image"
			content="https://bafybeif3o4ly27j3maxjj3lzyzabpb6wi4lqbgdzkr35thh26pwp5rvpre.ipfs.w3s.link/Group%201533213732.jpg">

		<!-- Twitter Meta Tags -->
		<meta name="twitter:card" content="summary_large_image">
		<meta property="twitter:domain" content="f-s.fun">
		<meta property="twitter:url" content="https://f-s.fun/">
		<meta name="twitter:title" content="FunShot - Kebahagiaan yang tidak diketahui orang lain">
		<meta name="twitter:description"
			content="Unduh FunShot, platform drama pendek dengan Neymar sebagai duta global. Tonton & hasilkan F-Bean sekarang!">
		<meta name="twitter:image"
			content="https://bafybeif3o4ly27j3maxjj3lzyzabpb6wi4lqbgdzkr35thh26pwp5rvpre.ipfs.w3s.link/Group%201533213732.jpg">
		<script>
			var coverSupport = 'CSS' in window && typeof CSS.supports === 'function' && (CSS.supports('top: env(a)') ||
				CSS.supports('top: constant(a)'))
			document.write(
				'<meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0' +
				(coverSupport ? ', viewport-fit=cover' : '') + '" />')
		</script>
		<title></title>
		<link rel="icon" href="./static/image/logo1x.png" />
		<!--preload-links-->
		<!--app-context-->
		<script src="https://res.openinstall.com/openinstall.js"></script>
	</head>
	<body>
		<div id="app"><!--app-html--></div>
		<script type="module" src="/main.js"></script>
	</body>
</html>