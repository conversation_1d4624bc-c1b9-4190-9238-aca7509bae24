@at-root {
  :root,
  page {
    --swiper-pagination-color: #007aff;
    --swiper-pagination-left: auto;
    --swiper-pagination-right: 8px;
    --swiper-pagination-bottom: 8px;
    --swiper-pagination-top: auto;
    --swiper-pagination-fraction-color: inherit;
    --swiper-pagination-progressbar-bg-color: rgb(0 0 0 / 25%);
    --swiper-pagination-progressbar-size: 4px;
    --swiper-pagination-bullet-size: 8px;
    --swiper-pagination-bullet-width: 8px;
    --swiper-pagination-bullet-height: 8px;
    --swiper-pagination-bullet-border-radius: 50%;
    --swiper-pagination-bullet-inactive-color: #000;
    --swiper-pagination-bullet-inactive-opacity: 0.2;
    --swiper-pagination-bullet-opacity: 1;
    --swiper-pagination-bullet-horizontal-gap: 4px;
    --swiper-pagination-bullet-vertical-gap: 6px;
  }
}

.swiper-pagination {
  position: absolute;
  z-index: 10;
  text-align: center;
  transition: 300ms opacity;
  transform: translate3d(0, 0, 0);

  &.swiper-pagination-hidden {
    opacity: 0;
  }

  .swiper-pagination-disabled > &,
  &.swiper-pagination-disabled {
    display: none !important;
  }
}

/* Common Styles */
.swiper-pagination-fraction,
.swiper-pagination-custom,
.swiper-horizontal > .swiper-pagination-bullets,
.swiper-pagination-bullets.swiper-pagination-horizontal {
  top: var(--swiper-pagination-top, auto);
  bottom: var(--swiper-pagination-bottom, 8px);
  left: 0;
  width: 100%;
}

/* Bullets */
.swiper-pagination-bullets-dynamic {
  overflow: hidden;
  font-size: 0;

  .swiper-pagination-bullet {
    position: relative;
    transform: scale(0.33);
  }

  .swiper-pagination-bullet-active {
    transform: scale(1);
  }

  .swiper-pagination-bullet-active-main {
    transform: scale(1);
  }

  .swiper-pagination-bullet-active-prev {
    transform: scale(0.66);
  }

  .swiper-pagination-bullet-active-prev-prev {
    transform: scale(0.33);
  }

  .swiper-pagination-bullet-active-next {
    transform: scale(0.66);
  }

  .swiper-pagination-bullet-active-next-next {
    transform: scale(0.33);
  }
}

.swiper-pagination-bullet {
  display: inline-block;
  width: var(
    --swiper-pagination-bullet-width,
    var(--swiper-pagination-bullet-size, 8px)
  );
  height: var(
    --swiper-pagination-bullet-height,
    var(--swiper-pagination-bullet-size, 8px)
  );
  background: var(--swiper-pagination-bullet-inactive-color, #000);
  border-radius: var(--swiper-pagination-bullet-border-radius, 50%);
  opacity: var(--swiper-pagination-bullet-inactive-opacity, 0.2);

  @at-root button#{&} {
    padding: 0;
    margin: 0;
    appearance: none;
    border: none;
    box-shadow: none;
  }

  .swiper-pagination-clickable & {
    cursor: pointer;
  }

  &:only-child {
    display: none !important;
  }
}

.swiper-pagination-bullet-active {
  background: var(--swiper-pagination-color, var(--swiper-theme-color));
  opacity: var(--swiper-pagination-bullet-opacity, 1);
}

.swiper-vertical > .swiper-pagination-bullets,
.swiper-pagination-vertical.swiper-pagination-bullets {
  top: 50%;
  right: var(--swiper-pagination-right, 8px);
  left: var(--swiper-pagination-left, auto);
  transform: translate3d(0, -50%, 0);

  .swiper-pagination-bullet {
    display: block;
    margin: var(--swiper-pagination-bullet-vertical-gap, 6px) 0;
  }

  &.swiper-pagination-bullets-dynamic {
    top: 50%;
    width: 8px;
    transform: translateY(-50%);

    .swiper-pagination-bullet {
      display: inline-block;
      transition:
        200ms transform,
        200ms top;
    }
  }
}

.swiper-horizontal > .swiper-pagination-bullets,
.swiper-pagination-horizontal.swiper-pagination-bullets {
  .swiper-pagination-bullet {
    margin: 0 var(--swiper-pagination-bullet-horizontal-gap, 4px);
  }

  &.swiper-pagination-bullets-dynamic {
    left: 50%;
    white-space: nowrap;
    transform: translateX(-50%);

    .swiper-pagination-bullet {
      transition:
        200ms transform,
        200ms left;
    }
  }
}

.swiper-horizontal.swiper-rtl
  > .swiper-pagination-bullets-dynamic
  .swiper-pagination-bullet {
  transition:
    200ms transform,
    200ms right;
}

/* Fraction */
.swiper-pagination-fraction {
  color: var(--swiper-pagination-fraction-color, inherit);
}

/* Progress */
.swiper-pagination-progressbar {
  position: absolute;
  background: var(--swiper-pagination-progressbar-bg-color, rgb(0 0 0 / 25%));

  .swiper-pagination-progressbar-fill {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--swiper-pagination-color, var(--swiper-theme-color));
    transform: scale(0);
    transform-origin: left top;
  }

  .swiper-rtl & .swiper-pagination-progressbar-fill {
    transform-origin: right top;
  }

  .swiper-horizontal > &,
  &.swiper-pagination-horizontal,
  .swiper-vertical > &.swiper-pagination-progressbar-opposite,
  &.swiper-pagination-vertical.swiper-pagination-progressbar-opposite {
    top: 0;
    left: 0;
    width: 100%;
    height: var(--swiper-pagination-progressbar-size, 4px);
  }

  .swiper-vertical > &,
  &.swiper-pagination-vertical,
  .swiper-horizontal > &.swiper-pagination-progressbar-opposite,
  &.swiper-pagination-horizontal.swiper-pagination-progressbar-opposite {
    top: 0;
    left: 0;
    width: var(--swiper-pagination-progressbar-size, 4px);
    height: 100%;
  }
}

.swiper-pagination-lock {
  display: none;
}
