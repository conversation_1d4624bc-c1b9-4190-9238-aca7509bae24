export type SwiperEmits = [
  '_beforeBreakpoint',
  '_containerClasses',
  '_slideClass',
  '_slideClasses',
  '_swiper',
  '_freeModeNoMomentumRelease',
  'activeIndexChange',
  'afterInit',
  'autoplay',
  'autoplayStart',
  'autoplayStop',
  'autoplayPause',
  'autoplayResume',
  'autoplayTimeLeft',
  'beforeDestroy',
  'beforeInit',
  'beforeLoopFix',
  'beforeResize',
  'beforeSlideChangeStart',
  'beforeTransitionStart',
  'breakpoint',
  'changeDirection',
  'click',
  'disable',
  'doubleTap',
  'doubleClick',
  'destroy',
  'enable',
  'fromEdge',
  'hashChange',
  'hashSet',
  'init',
  'keyPress',
  'lock',
  'loopFix',
  'momentumBounce',
  'navigationHide',
  'navigationShow',
  'navigationPrev',
  'navigationNext',
  'observerUpdate',
  'orientationchange',
  'paginationHide',
  'paginationRender',
  'paginationShow',
  'paginationUpdate',
  'progress',
  'reachBeginning',
  'reachEnd',
  'realIndexChange',
  'resize',
  'scroll',
  'scrollbarDragEnd',
  'scrollbarDragMove',
  'scrollbarDragStart',
  'setTransition',
  'setTranslate',
  'slidesUpdated',
  'slideChange',
  'slideChangeTransitionEnd',
  'slideChangeTransitionStart',
  'slideNextTransitionEnd',
  'slideNextTransitionStart',
  'slidePrevTransitionEnd',
  'slidePrevTransitionStart',
  'slideResetTransitionStart',
  'slideResetTransitionEnd',
  'sliderMove',
  'sliderFirstMove',
  'slidesLengthChange',
  'slidesGridLengthChange',
  'snapGridLengthChange',
  'snapIndexChange',
  'swiper',
  'tap',
  'toEdge',
  'touchEnd',
  'touchMove',
  'touchMoveOpposite',
  'touchStart',
  'transitionEnd',
  'transitionStart',
  'unlock',
  'update',
  'virtualUpdate',
  'zoomChange',
  'update:list',
  '_freeModeStaticRelease'
]
