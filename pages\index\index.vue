<template>
	<view v-if="ispc === true" class="content wrapper index_body" style="padding-top: 0;">
		<!-- <p style="color: white;">{{ $t('message.hello') }}</p> -->
		<scroll-view v-if="ispc === true" class="scroll-container" scroll-y="true" style="height: auto"
			:scroll-into-view="toView" @scroll="scrollTo11" :scroll-top="sctop" scroll-with-animation="true"
			:show-scrollbar="false">
			<header class="header" v-if="ispc === true" style="height: 230rpx;padding-top: 60rpx;">
				<view class="header_box">
					<view class="logo_appname_boxbody">
						<view class="logo_appname_box" @click="menua(-1)">
							<view class="logo_box">
								<image src="../../static/image/logo3x.png" mode=""></image>
							</view>
							<view class="appname_box">
								<view class="appname">
									{{ $t('logotit') }}
								</view>
								<view class="log_tit">
									{{ $t('logotitmes') }}
								</view>
							</view>
						</view>
					</view>
					<view class="header_menu_boxbody" @mouseleave="menul">
						<view class="header_menu_box" :class="menuactive === index ? 'active' : ''"
							v-for="(item,index) in menulist" :key="index" @click="menua(index)"
							@mouseenter="menum(index)">
							<view class="menu_tit">
								<view v-if="item !== 'Download'" class="">
									{{$t('header.' + item)}}
								</view>
								<view v-else class="hot_download">
									<view class="hotimg">
										<image src="../../static/image/Vector.png" mode=""></image>
									</view>
									<view class="">
										{{$t('header.' + item)}}
									</view>
								</view>
							</view>
							<view class="a_liner">

							</view>
						</view>
					</view>
					<view class="lang_contact_box">
						<view class="lang_box_body">
							<view class="lang_imgbox">
								<image src="../../static/image/SVG.png" mode=""></image>
							</view>
							<view class="lang_choose_boxbody" @click="showlangchange">
								<view class="lang_mes">
									{{$t(lan)}}
								</view>
								<view class="iconfont icon-xiala xiala">

								</view>
							</view>
							<view class="langchange_box_body" style="top: 180rpx;">
								<view class="lang_box" v-for="(item,index) in langlist" :key="index"
									@click="lanchange(item)">
									{{$t('lang.' + item)}}
								</view>
							</view>
						</view>
						<view class="contact_box" @click="gotowburl">
							<view class="zzc">

							</view>
							<view class="contxt">
								{{$t('ContactUs')}}
							</view>
						</view>
					</view>
				</view>
			</header>
			<view v-else class="appheader">
				<view class="left_apphaderlogo">
					<view class="logo_appbox">
						<image src="../../static/image/logo3x.png" mode=""></image>
					</view>
					<view class="appname_box">
						<view class="appname_t">
							{{$t('logotit')}}
						</view>
						<view class="appname_b">
							{{$t('logotitmes')}}
						</view>
					</view>
				</view>
				<view class="hotapp_box" @click="gotopage">
					<image src="../../static/image/Group 1533208842.png" mode=""></image>
				</view>
			</view>
			<!-- 			<view v-show="langch" class="langchange_box_body">
				<view class="lang_box" v-for="(item,index) in langlist" :key="index" @click="lanchange(item)">
					{{$t('lang.' + item)}}
				</view>
			</view> -->
			<view class="pc_banner_boxbody" id="banner">
				<swiper class="spswiper swiper-box" style="height: 100%;" circular :indicator-dots="ispc"
					indicator-color="rgba(255, 255, 255, 1)" :autoplay="true" :interval="2000" :duration="500">
					<swiper-item>
						<view v-if="ispc === true" class="banner_boxbody">
							<view class="top_tit_mes_box"
								style="position: absolute;top: 0;z-index: 999;padding-top: 0;">
								<view class="top_tit_mes">
									<view class="top_tit">
										{{$t('pcBanner.banner1.Incentive')}}
										<view class="" style="color: #BAFC51;display: inline">
											{{$t('pcBanner.banner1.Worldwide')}}
										</view>
									</view>
									<view class="mes_box">
										{{$t('pcBanner.banner1.msg')}}
									</view>
								</view>
							</view>
							<view class="banner_swiper_boxbody">
								<image src="../../static/image/banner/图层 11asdsa 1.png" mode="widthFix"></image>
							</view>
						</view>
						<view v-else class="banner_boxbody">
							<view class="top_tit_mes_box">
								<view class="top_tit_mes">
									<view class="top_tit">
										{{$t('pcBanner.banner1.Incentive')}}
										<view class="" style="color: #BAFC51;display: inline">
											{{$t('pcBanner.banner1.Worldwide')}}
										</view>
									</view>
									<view class="mes_box">
										{{$t('pcBanner.banner1.msg')}}
									</view>
								</view>
							</view>
							<view class="banner_swiper_boxbody">
								<image src="../../static/image/banner/图层 11asdsa 1.png" mode="widthFix"></image>
							</view>
						</view>
					</swiper-item>
					<swiper-item>
						<view class="banner_boxbody" v-if="ispc === true">
							<view class="banner_swiper_boxbody">
								<image src="../../static/image/banner/图层 1 拷s2daa贝.png" mode="widthFix"></image>
							</view>
							<view class="top_tit_mes_box"
								style="position: absolute;bottom: 0;z-index: 999;padding-top: 160rpx;">
								<view class="top_tit_mes">
									<view class="top_tit">
										{{$t('pcBanner.banner2.Incentive')}}
										<view class="" style="color: #BAFC51;display: inline">
											{{$t('pcBanner.banner2.Worldwide')}}
										</view>
									</view>
									<view class="mes_box">
										{{$t('pcBanner.banner2.msg')}}
									</view>
								</view>
							</view>
						</view>
						<view v-else class="banner_boxbody">
							<view class="top_tit_mes_box">
								<view class="top_tit_mes">
									<view class="top_tit">
										{{$t('pcBanner.banner2.Incentive')}}
										<view class="" style="color: #BAFC51;display: inline">
											{{$t('pcBanner.banner2.Worldwide')}}
										</view>
									</view>
									<view class="mes_box">
										{{$t('pcBanner.banner2.msg')}}
									</view>
								</view>
							</view>
							<view class="banner_swiper_boxbody">
								<image src="../../static/image/banner/图层 1 拷s2daa贝.png" mode="widthFix"></image>
							</view>
						</view>
					</swiper-item>
					<swiper-item>
						<view class="banner_boxbody" v-if="ispc === true">
							<view class="banner_swiper_boxbody_b3">
								<image src="../../static/image/banner/图层 1dsa 1.png" mode="widthFix"></image>
								<view class="tit_mes_zinx">
									<view class="blogo_boxbody">
										<image src="../../static/image/banner/Group 1533213357.png" mode=""></image>
									</view>
									<view class="bold_tit_box">
										{{$t('pcBanner.banner3.First')}}
										<view style="color: #BAFC51;display: inline">{{$t('pcBanner.banner3.Watch')}}
										</view>
										{{$t('pcBanner.banner3.Platform')}}
									</view>
									<view class="b3txt">
										{{$t('pcBanner.banner3.msg')}}
									</view>
								</view>
							</view>
						</view>
						<view v-else class="banner_boxbody">
							<view class="top_tit_mes_box">
								<view class="top_tit_mes">
									<view class="top_tit">
										{{$t('pcBanner.banner3.First')}}
										<view style="color: #BAFC51;display: inline">{{$t('pcBanner.banner3.Watch')}}
										</view>
										{{$t('pcBanner.banner3.Platform')}}
									</view>
									<view class="mes_box">
										{{$t('pcBanner.banner3.msg')}}
									</view>
								</view>
							</view>
							<view class="banner_swiper_boxbody">
								<image src="../../static/image/banner/图层 1dsa 1.png" mode="widthFix"></image>
							</view>
						</view>
					</swiper-item>
					<swiper-item>
						<view class="banner_boxbody" v-if="ispc === true">
							<view class="banner_swiper_boxbody_b3">
								<image src="../../static/image/banner/图层 1dsad.png" mode="widthFix"></image>
								<view class="tit_mes_zinx">
									<view class="blogo_boxbody">
										<image src="../../static/image/banner/Group 1533213357.png" mode=""></image>
									</view>
									<view class="bold_tit_box">
										<view style="color: #BAFC51;display: inline">{{$t('pcBanner.banner4.Why')}}
										</view>
										{{$t('pcBanner.banner4.FunShot')}}
									</view>
									<view class="b3txt">
										{{$t('pcBanner.banner4.msg')}}
									</view>
								</view>
							</view>
						</view>
						<view v-else class="banner_boxbody">
							<view class="top_tit_mes_box">
								<view class="top_tit_mes">
									<view class="top_tit">
										<view style="color: #BAFC51;display: inline">{{$t('pcBanner.banner4.Why')}}
										</view>
										{{$t('pcBanner.banner4.FunShot')}}
									</view>
									<view class="mes_box">
										{{$t('pcBanner.banner4.msg')}}
									</view>
								</view>
							</view>
							<view class="banner_swiper_boxbody">
								<image src="../../static/image/banner/图层 1dsad.png" mode="widthFix"></image>
							</view>
						</view>
					</swiper-item>
				</swiper>
				<view class="hmban-mouse-wrap hidden-lg-down is-light">
					<view class="hmban-mouse">
						<view class="dw d1"></view>
						<view class="dw d2"></view>
					</view>
				</view>
			</view>
			<view id="Overview" class="upgrades_boxbody">
				<view class="upgrades_box_body">
					<view class="upgrades_bold_tit">
						<view class="u_bold_t">
							<view class="" style="color: #BAFC51;display: inline">
								{{$t('upgrades.tit1')}}
							</view> {{$t('upgrades.tit2')}}
						</view>
						<view class="u_m_t">
							{{$t('upgrades.tit3')}}
						</view>
					</view>
					<view class="upgrades_img_mes_boxbody">
						<view class="left_chose_boxbody">
							<view class="left_chose_box" :class="leftchoseinx === index ? 'active' : ''"
								v-for="(item,index) in leftchose" :key="index" @click="leftcho(index)"
								@mousewheel.stop="handleMouseWheel">
								<!-- {{item.ltit}} -->
								{{$t('upgrades.leftchose[' + index + '].ltit')}}
							</view>
						</view>
						<view v-if="ispc === true" class="right_bo_mes_boxbody" @mousewheel.stop="handleMouseWheel">
							<swiper class="swiper" :current="leftchoseinx" :vertical="ispc" circular interval="5000"
								duration="500">
								<swiper-item v-for="(item,index) in leftchose" :key="index">
									<view class="spitem" :class="leftchoseinx === index ? 'active' : ''">
										<view class="bo_left_img_box">
											<image :src="item.imgurl" mode=""></image>
										</view>
										<view class="bo_right_mes_box_body">
											<view class="mes_box_body">
												<view class="mes_box_tit">
													<!-- {{item.rtit}} -->
													{{$t('upgrades.leftchose[' + index + '].rtit')}}
												</view>
												<view class="mes_box_mes" style="white-space: pre-line;">
													<!-- {{item.rmes}} -->
													{{$t('upgrades.leftchose[' + index + '].rmes')}}
												</view>
											</view>
											<view class="topview_bm_box_body">
												<view class="topview_bm_box">
													<view class="tb" :class="tbinx === index ? 'active' : ''"
														v-for="(item,index) in tb" :key="index" @click="tbclick(index)">
														{{item}}
													</view>
												</view>
												<view class="anniu">
													<image src="../../static/image/Button - Next slide.png" mode="">
													</image>
												</view>
											</view>
										</view>
									</view>
								</swiper-item>

							</swiper>

						</view>
						<view v-else class="aas" style="width: 100%;margin-top: 40rpx;">
							<swiper class="swiper" :current="leftchoseinx" :vertical="ispc" circular interval="5000"
								duration="500" @change="currentchange">
								<swiper-item v-for="(item,index) in leftchose" :key="index">
									<view class="right_bo_mes_boxbody">
										<view class="bo_left_img_box">
											<image :src="item.imgurl" mode=""></image>
										</view>
										<view class="bo_right_mes_box_body">
											<view class="mes_box_body">
												<view class="mes_box_tit">
													<!-- {{item.rtit}} -->
													{{$t('upgrades.leftchose[' + index + '].rtit')}}
												</view>
												<view class="mes_box_mes" style="white-space: pre-line;">
													<!-- {{item.rmes}} -->
													{{$t('upgrades.leftchose[' + index + '].rmes')}}
												</view>
											</view>
											<view class="topview_bm_box_body">
												<view class="topview_bm_box">
													<view class="tb" :class="tbinx === index ? 'active' : ''"
														v-for="(item,index) in tb" :key="index" @click="tbclick(index)">
														{{item}}
													</view>
												</view>
												<view class="anniu">
													<image src="../../static/image/Button - Next slide.png" mode="">
													</image>
												</view>
											</view>
										</view>
									</view>

								</swiper-item>

							</swiper>
						</view>
					</view>
				</view>
			</view>
			<view id="Performance" class="funshot_box_body">
				<view class="funshot_boxbody">
					<view class="funshot_toptit">
						<view v-if="ispc=== true" class="" style="color: #BAFC51;display: inline">
							{{$t('funshot.tit1')}}
						</view>
						<view v-else class="" style="color: #BAFC51;">
							{{$t('funshot.tit1')}}
						</view>
						{{$t('funshot.tit2')}}
					</view>
					<view class="funshot_bot_mesboxbody">
						<view class="funshotb_left">
							<view class="flf_box" v-for="(item,index) in flfdata" :key="index" @click="flfclick(index)"
								@mousewheel.stop="handleMouseWheel1">
								<view class="flf_b1" v-if="flfinx !== index">
									<!-- {{item.tit}} -->
									{{$t('funshot.flfdata[' + index + '].tit')}}
								</view>
								<view v-else class="flf_b2">
									<view class="flf_b2_tit">
										<!-- {{item.tit}} -->
										{{$t('funshot.flfdata[' + index + '].tit')}}
									</view>
									<view class="flf_b2_mes">
										<!-- {{item.mes}} -->
										{{$t('funshot.flfdata[' + index + '].mes')}}
									</view>
								</view>
							</view>
						</view>
						<view class="app_funshotb_boxbody" v-show="ispc === false">
							<view class="app_fbimgbox">
								<video id="myVideo" src="../../static/image/51593_1745628702_raw.mp4" :loop="true"
									:controls="true"></video>
							</view>
							<swiper class="swiper" style="height: 156rpx;" :current="flfinx" :vertical="true" circular
								interval="5000" duration="500">
								<swiper-item v-for="(item,index) in flfdata" :key="index">
									<view class="app_fbsjbox">
										<view class="to_fbsjb">
											<view class="l_fbsjb">
												{{item.num}}
											</view>
											<view class="l_fbsmes">
												#Funshot Platform users have accumulated breakthroughs (as of April
												2025)
											</view>
										</view>
										<view class="fh">
											{{item.dw}}
										</view>
									</view>
								</swiper-item>

							</swiper>

						</view>
						<view class="funshotb_cen" v-show="ispc">
							<!-- <image src="../../static/image/Group 1533213380.png" mode=""></image> -->
							<video src="../../static/image/51593_1745628702_raw.mp4" :loop="true"
								:controls="true"></video>
						</view>
						<view class="ccswiper" v-show="ispc" style="flex: 1;height: 100%;margin-left: 40rpx;"
							@mousewheel.stop="handleMouseWheel1">

							<swiper class="swiper" :current="flfinx" :vertical="true" circular interval="5000"
								duration="500">
								<swiper-item v-for="(item,index) in flfdata" :key="index">
									<view class="funshotb_right" :class="flfinx === index ? 'active' : ''">
										<view class="num_box_body">
											<view class="num_box">
												{{item.num}}
											</view>
											<view class="l_mes">
												· Over 15.87 million users as of April 2025
											</view>
										</view>
										<view class="dw_box">
											{{item.dw}}
										</view>
									</view>
								</swiper-item>

							</swiper>

						</view>

					</view>
				</view>
			</view>
			<view class="aliexpress_box_body">
				<view class="aliexpress_boxbody">
					<view class="left_ali_tab_boxbody">
						<view class="ali_tab_boxbody" :class="aliinx === index ? 'active' : ''"
							v-for="(item,index) in alitab" :key="index" @click="aliclick(index)"
							@mousewheel.stop="handleMouseWheel3">
							<image v-if="ispc === true" :src="item.url" mode="heightFix" class="alitabimg"></image>
							<image v-else :src="item.url" mode="" class="alitabimg" style="width: 80%;">
							</image>
						</view>
					</view>

					<swiper v-if="ispc === true" class="swiper" style="width: 1300rpx;" :current="aliinx"
						:vertical="true" circular interval="5000" duration="500" @mousewheel.stop="handleMouseWheel3">
						<swiper-item v-for="(item,index) in alitab" :key="index">
							<!-- <image v-if="ispc === true" src="../../static/image/Container.png" mode="heightFix"></image> -->
							<view class="right_ali_imgboxbody"
								style="width: 620px;display: flex;flex-direction: column;">
								<!-- 								<image v-if="ispc === true" src="../../static/image/Container.png" mode="heightFix">
								</image>
								<image v-else src="../../static/image/Container.png" mode=""></image> -->
								<view class="yinhao" style="width: 36px;height: 28px;">
									<image src="../../static/image/bi_quote.svg.png" mode=""></image>
								</view>
								<view class="rmesg"
									style="box-sizing: border-box;padding-left: 60px;width: 100%;height: auto;font-size: 26px;">
									{{$t('aliexpress.tit1')}}
								</view>
								<view class=""
									style="box-sizing: border-box;padding-left: 60px;width: 100%;height: auto;font-size: 16px;margin-top: 28px;">
									{{$t('aliexpress.tit2')}}
								</view>
							</view>
						</swiper-item>

					</swiper>
					<swiper v-else class="swiper" style="width: 100%;" :current="aliinx" :vertical="true" circular
						interval="5000" duration="500">
						<swiper-item v-for="(item,index) in alitab" :key="index">
							<!-- <image v-if="ispc === true" src="../../static/image/Container.png" mode="heightFix"></image> -->
							<view class="right_ali_imgboxbody">
								<image v-if="ispc === true" src="../../static/image/Container.png" mode="heightFix">
								</image>
								<image v-else src="../../static/image/Container.png" mode=""></image>
							</view>
						</swiper-item>

					</swiper>
					<!-- 				<view class="right_ali_imgboxbody" :class="aliinx === 0 ? 'active' : ''">
							<image v-if="ispc === true" src="../../static/image/Container.png" mode="heightFix"></image>
							<image v-else src="../../static/image/Container.png" mode=""></image>
						</view>
						<view class="right_ali_imgboxbody" :class="aliinx === 1 ? 'active' : ''">
							<image v-if="ispc === true" src="../../static/image/Container.png" mode="heightFix"></image>
							<image v-else src="../../static/image/Container.png" mode=""></image>
						</view>
						<view class="right_ali_imgboxbody" :class="aliinx === 2 ? 'active' : ''">
							<image v-if="ispc === true" src="../../static/image/Container.png" mode="heightFix"></image>
							<image v-else src="../../static/image/Container.png" mode=""></image>
						</view> -->
				</view>
			</view>
			<view id="Features" class="Full-chain">
				<view class="full_chain_bodbody">
					<view class="full_toptit">
						<!-- {{fullxz[fullxzinx].toptit}} -->
						{{$t('fullxz[' + fullxzinx + '].toptit')}}
					</view>
					<view class="full_xz_boxbody">
						<!-- 						<view class="xz_box_body" :class="index === 3 ? 'xz1' : ''" v-for="(item,index) in fullxz"
							:key="index" @click="fullxzclick(index)">
							<view class="fullxz_an" :class="fullxzinx === index ? 'active' : ''">
								{{$t('fullxz[' + fullxzinx + '].xzmes')}}
							</view>
							<view class="youjiantou" v-if="item.xzmes !== 'Data Growth'">
								<image src="../../static/image/Image.png" mode=""></image>
							</view>
						</view> -->
						<view class="xz_box_body" @click="fullxzclick(0)">
							<view class="fullxz_an" :class="fullxzinx === 0 ? 'active' : ''">
								<!-- {{item.xzmes}} -->
								{{$t('fullxz[0].xzmes')}}
							</view>
							<view class="youjiantou">
								<image src="../../static/image/Image.png" mode=""></image>
							</view>
						</view>
						<view class="xz_box_body" @click="fullxzclick(1)">
							<view class="fullxz_an" :class="fullxzinx === 1 ? 'active' : ''">
								<!-- {{item.xzmes}} -->
								{{$t('fullxz[1].xzmes')}}
							</view>
							<view class="youjiantou">
								<image src="../../static/image/Image.png" mode=""></image>
							</view>
						</view>
						<view class="xz_box_body" @click="fullxzclick(2)">
							<view class="fullxz_an" :class="fullxzinx === 2 ? 'active' : ''">
								<!-- {{item.xzmes}} -->
								{{$t('fullxz[2].xzmes')}}
							</view>
							<view class="youjiantou">
								<image src="../../static/image/Image.png" mode=""></image>
							</view>
						</view>
						<view class="xz_box_body xz1" @click="fullxzclick(3)">
							<view class="fullxz_an" :class="fullxzinx === 3 ? 'active' : ''">
								<!-- {{item.xzmes}} -->
								{{$t('fullxz[3].xzmes')}}
							</view>
							<!-- 							<view class="youjiantou">
								<image src="../../static/image/Image.png" mode=""></image>
							</view> -->
						</view>
					</view>
					<view class="full_xz_botmes_boxbody">
						<view class="bot_leftimgbox" :class="fullxzinx === 2 ? 'lf1' :''">
							<image :src="fullxz[fullxzinx].url" :mode="ispc===true?'heightFix':'widthFix'"></image>
						</view>
						<view class="bot_rightmgbox">
							<view class="tit1">
								<!-- {{fullxz[fullxzinx].tit1}} -->
								{{$t('fullxz[' + fullxzinx + '].tit1')}}
							</view>
							<view class="tit2">
								<!-- {{fullxz[fullxzinx].tit2}} -->
								{{$t('fullxz[' + fullxzinx + '].tit2')}}
							</view>
							<view class="fmes">
								<!-- {{fullxz[fullxzinx].mes}} -->
								{{$t('fullxz[' + fullxzinx + '].mes')}}
							</view>
							<view class="learnmore_boxbody" v-if="fullxzinx === 0 || fullxzinx === 1">
								<view class="learnmore_box" @click="gotowburl">
									{{$t('learn')}}
								</view>
								<view class="xiangyou" v-show="ispc">
									<image src="../../static/image/Button - Next slide.png" mode=""></image>
								</view>
							</view>
							<view class="learnmore_boxbody" v-if="fullxzinx === 2">
								<view class="learnchange_boxbody">
									<view class="learnchange_box" :class="lchaninx === index ? 'active' : ''"
										v-for="(item,index) in learnchange" :key="index" @click="lanclick(index)">
										{{item}}
									</view>
								</view>
								<view class="xiangyou">
									<image src="../../static/image/Button - Next slide.png" mode=""></image>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
			<view id="Advantages" class="Ultimate_Vision">
				<view class="vision_top_mesbox">
					<view class="vt_toptit">
						<view class="" style="color: #BAFC51;display: inline">
							{{$t('Ultimate.tit1')}}
						</view> {{$t('Ultimate.tit2')}}
					</view>
					<view class="vt_botmes">
						{{$t('Ultimate.tit3')}}
					</view>
				</view>
				<view class="vision_bot_swiperboxbody">
					<view class="vision_bot_swiperbox">
						<view class="vi_limgbox">
							<image :src="viimg[currentinx]" mode=""></image>
						</view>
						<view class="vi_rswiperboxbody">
							<swiper class="spswiper1 viswpier swiper-box" style="height: 100%;" circular
								:indicator-dots="true" indicator-color="rgba(255, 255, 255, 1)" :vertical="ispc"
								:autoplay="true" :interval="2000" :duration="500" :current="currentinx"
								@change="swiperchang">
								<swiper-item v-for="i in 3" :key="i">
									<view class="vi_rswiperbox">
										<view class="vi_toptit">
											{{$t('Ultimate.vision['+ (i-1) + '].tit1')}}
										</view>
										<view class="vi_mes">
											{{$t('Ultimate.vision['+ (i-1) + '].mes')}}
										</view>
										<view class="vi_toptit">
											{{$t('Ultimate.vision['+ (i-1) + '].tit2')}}
										</view>
										<view class="vi_mes1">
											<view class="mes1_mes">
												<view class="yuandian"></view>
												<view class="mm">
													{{$t('Ultimate.vision['+(i-1) + '].mm1')}}
												</view>
											</view>
											<view class="mes1_mes">
												<view class="yuandian"></view>
												<view class="mm">
													{{$t('Ultimate.vision['+ (i-1) + '].mm2')}}
												</view>
											</view>
											<view class="mes1_mes">
												<view class="yuandian"></view>
												<view class="mm">
													{{$t('Ultimate.vision['+ (i-1) + '].mm3')}}
												</view>
											</view>
											<view class="mes1_mes">
												<view class="yuandian"></view>
												<view class="mm">
													{{$t('Ultimate.vision['+ (i-1) + '].mm4')}}
												</view>
											</view>
										</view>
									</view>
								</swiper-item>
							</swiper>

						</view>
					</view>
				</view>
			</view>
			<view class="revenue_model">
				<view class="revenue_model_boxbody">
					<view class="re_top_tit">
						<view v-if="ispc===true" class="" style="color: #BAFC51;display: inline">
							{{$t('revenue.tit1')}}
						</view>
						<view v-else class="" style="color: #BAFC51;">
							{{$t('revenue.tit1')}}
						</view>
						{{$t('revenue.tit2')}}
					</view>
					<view class="re_top_mes">
						{{$t('revenue.tit3')}}
					</view>
					<view class="diverse_boxbody pshop-path-box web hidden-lg-down">
						<!-- 背景∞图 -->
						<image v-if="lan === 'lang.Eenglish'" src="../../static/image/Container (3).png" class="bg"
							mode=""></image>
						<image v-else-if="lan === 'lang.Chinese'" src="../../static/image/Container (2).png" class="bg1"
							mode=""></image>
						<image v-else-if="lan === 'lang.Indonesian'" src="../../static/image/Container (4).png"
							class="bg" mode="widthFix"></image>
						<!-- 箭头svg -->
						<view class="svgbox" id="valWebSvgBox" v-if="ispc===true">
							<svg class="shopAvgWeb" id="shopVoute" x="0px" y="0px" viewBox="0 0 744 350"
								style="opacity: 1;" fill="none" xmlns="http://www.w3.org/2000/svg">
								<path id="shopPath" opacity="0" class="st0"
									d="M173.5,33.7C137,33.4,100.3,47.2,72.4,75.1l0,0c-55.2,55.2-55.2,144.6,0,199.8l0,0c55.2,55.2,144.6,55.2,199.8,0L472,75.1c55.2-55.2,144.6-55.2,199.8,0l0,0c55.2,55.2,55.2,144.6,0,199.8l0,0c-55.2,55.2-144.6,55.2-199.8,0L272.2,75.1C244.9,47.8,209.3,34,173.5,33.7">
								</path>
								<g id="shopArrow">
									<g class="arrow a1">
										<path d="M 0 0 L 8 8 L0 16" fill="none" stroke="#fff" stroke-opacity="0.5"
											stroke-width="3" stroke-linecap="round" stroke-linejoin="round"
											style="transform:translateY(-8px)"></path>
										<animateMotion dur="10s" begin="0s" repeatCount="indefinite" rotate="auto">
											<mpath xlink:href="#shopPath"></mpath>
										</animateMotion>
									</g>
									<g class="arrow a2">
										<path d="M 0 0 L 8 8 L0 16" fill="none" stroke="#fff" stroke-opacity="0.5"
											stroke-width="3" stroke-linecap="round" stroke-linejoin="round"
											style="transform:translateY(-8px)"></path>
										<animateMotion dur="10s" begin="0.33s" repeatCount="indefinite" rotate="auto">
											<mpath xlink:href="#shopPath"></mpath>
										</animateMotion>
									</g>
									<g class="arrow a3">
										<path d="M 0 0 L 8 8 L0 16" fill="none" stroke="#fff" stroke-opacity="0.5"
											stroke-width="3" stroke-linecap="round" stroke-linejoin="round"
											style="transform:translateY(-8px)"></path>
										<animateMotion dur="10s" begin="0.66s" repeatCount="indefinite" rotate="auto">
											<mpath xlink:href="#shopPath"></mpath>
										</animateMotion>
									</g>
									<g class="arrow a4">
										<path d="M 0 0 L 8 8 L0 16" fill="none" stroke="#fff" stroke-opacity="0.5"
											stroke-width="3" stroke-linecap="round" stroke-linejoin="round"
											style="transform:translateY(-8px)"></path>
										<animateMotion dur="10s" begin="0.99s" repeatCount="indefinite" rotate="auto">
											<mpath xlink:href="#shopPath"></mpath>
										</animateMotion>
									</g>
									<g class="arrow a5">
										<path d="M 0 0 L 8 8 L0 16" fill="none" stroke="#fff" stroke-opacity="0.5"
											stroke-width="3" stroke-linecap="round" stroke-linejoin="round"
											style="transform:translateY(-8px)"></path>
										<animateMotion dur="10s" begin="1.32s" repeatCount="indefinite" rotate="auto">
											<mpath xlink:href="#shopPath"></mpath>
										</animateMotion>
									</g>
									<g class="arrow a6">
										<path d="M 0 0 L 8 8 L0 16" fill="none" stroke="#fff" stroke-opacity="0.5"
											stroke-width="3" stroke-linecap="round" stroke-linejoin="round"
											style="transform:translateY(-8px)"></path>
										<animateMotion dur="10s" begin="1.6500000000000001s" repeatCount="indefinite"
											rotate="auto">
											<mpath xlink:href="#shopPath"></mpath>
										</animateMotion>
									</g>
									<g class="arrow a7">
										<path d="M 0 0 L 8 8 L0 16" fill="none" stroke="#fff" stroke-opacity="0.5"
											stroke-width="3" stroke-linecap="round" stroke-linejoin="round"
											style="transform:translateY(-8px)"></path>
										<animateMotion dur="10s" begin="1.98s" repeatCount="indefinite" rotate="auto">
											<mpath xlink:href="#shopPath"></mpath>
										</animateMotion>
									</g>
									<g class="arrow a8">
										<path d="M 0 0 L 8 8 L0 16" fill="none" stroke="#fff" stroke-opacity="0.5"
											stroke-width="3" stroke-linecap="round" stroke-linejoin="round"
											style="transform:translateY(-8px)"></path>
										<animateMotion dur="10s" begin="2.31s" repeatCount="indefinite" rotate="auto">
											<mpath xlink:href="#shopPath"></mpath>
										</animateMotion>
									</g>
									<g class="arrow a9">
										<path d="M 0 0 L 8 8 L0 16" fill="none" stroke="#fff" stroke-opacity="0.5"
											stroke-width="3" stroke-linecap="round" stroke-linejoin="round"
											style="transform:translateY(-8px)"></path>
										<animateMotion dur="10s" begin="2.64s" repeatCount="indefinite" rotate="auto">
											<mpath xlink:href="#shopPath"></mpath>
										</animateMotion>
									</g>
									<g class="arrow a10">
										<path d="M 0 0 L 8 8 L0 16" fill="none" stroke="#fff" stroke-opacity="0.5"
											stroke-width="3" stroke-linecap="round" stroke-linejoin="round"
											style="transform:translateY(-8px)"></path>
										<animateMotion dur="10s" begin="2.97s" repeatCount="indefinite" rotate="auto">
											<mpath xlink:href="#shopPath"></mpath>
										</animateMotion>
									</g>
									<g class="arrow a11">
										<path d="M 0 0 L 8 8 L0 16" fill="none" stroke="#fff" stroke-opacity="0.5"
											stroke-width="3" stroke-linecap="round" stroke-linejoin="round"
											style="transform:translateY(-8px)"></path>
										<animateMotion dur="10s" begin="3.3000000000000003s" repeatCount="indefinite"
											rotate="auto">
											<mpath xlink:href="#shopPath"></mpath>
										</animateMotion>
									</g>
									<g class="arrow a12">
										<path d="M 0 0 L 8 8 L0 16" fill="none" stroke="#fff" stroke-opacity="0.5"
											stroke-width="3" stroke-linecap="round" stroke-linejoin="round"
											style="transform:translateY(-8px)"></path>
										<animateMotion dur="10s" begin="3.6300000000000003s" repeatCount="indefinite"
											rotate="auto">
											<mpath xlink:href="#shopPath"></mpath>
										</animateMotion>
									</g>
									<g class="arrow a13">
										<path d="M 0 0 L 8 8 L0 16" fill="none" stroke="#fff" stroke-opacity="0.5"
											stroke-width="3" stroke-linecap="round" stroke-linejoin="round"
											style="transform:translateY(-8px)"></path>
										<animateMotion dur="10s" begin="3.96s" repeatCount="indefinite" rotate="auto">
											<mpath xlink:href="#shopPath"></mpath>
										</animateMotion>
									</g>
									<g class="arrow a14">
										<path d="M 0 0 L 8 8 L0 16" fill="none" stroke="#fff" stroke-opacity="0.5"
											stroke-width="3" stroke-linecap="round" stroke-linejoin="round"
											style="transform:translateY(-8px)"></path>
										<animateMotion dur="10s" begin="4.29s" repeatCount="indefinite" rotate="auto">
											<mpath xlink:href="#shopPath"></mpath>
										</animateMotion>
									</g>
									<g class="arrow a15">
										<path d="M 0 0 L 8 8 L0 16" fill="none" stroke="#fff" stroke-opacity="0.5"
											stroke-width="3" stroke-linecap="round" stroke-linejoin="round"
											style="transform:translateY(-8px)"></path>
										<animateMotion dur="10s" begin="4.62s" repeatCount="indefinite" rotate="auto">
											<mpath xlink:href="#shopPath"></mpath>
										</animateMotion>
									</g>
									<g class="arrow a16">
										<path d="M 0 0 L 8 8 L0 16" fill="none" stroke="#fff" stroke-opacity="0.5"
											stroke-width="3" stroke-linecap="round" stroke-linejoin="round"
											style="transform:translateY(-8px)"></path>
										<animateMotion dur="10s" begin="4.95s" repeatCount="indefinite" rotate="auto">
											<mpath xlink:href="#shopPath"></mpath>
										</animateMotion>
									</g>
									<g class="arrow a17">
										<path d="M 0 0 L 8 8 L0 16" fill="none" stroke="#fff" stroke-opacity="0.5"
											stroke-width="3" stroke-linecap="round" stroke-linejoin="round"
											style="transform:translateY(-8px)"></path>
										<animateMotion dur="10s" begin="5.28s" repeatCount="indefinite" rotate="auto">
											<mpath xlink:href="#shopPath"></mpath>
										</animateMotion>
									</g>
									<g class="arrow a18">
										<path d="M 0 0 L 8 8 L0 16" fill="none" stroke="#fff" stroke-opacity="0.5"
											stroke-width="3" stroke-linecap="round" stroke-linejoin="round"
											style="transform:translateY(-8px)"></path>
										<animateMotion dur="10s" begin="5.61s" repeatCount="indefinite" rotate="auto">
											<mpath xlink:href="#shopPath"></mpath>
										</animateMotion>
									</g>
									<g class="arrow a19">
										<path d="M 0 0 L 8 8 L0 16" fill="none" stroke="#fff" stroke-opacity="0.5"
											stroke-width="3" stroke-linecap="round" stroke-linejoin="round"
											style="transform:translateY(-8px)"></path>
										<animateMotion dur="10s" begin="5.94s" repeatCount="indefinite" rotate="auto">
											<mpath xlink:href="#shopPath"></mpath>
										</animateMotion>
									</g>
									<g class="arrow a20">
										<path d="M 0 0 L 8 8 L0 16" fill="none" stroke="#fff" stroke-opacity="0.5"
											stroke-width="3" stroke-linecap="round" stroke-linejoin="round"
											style="transform:translateY(-8px)"></path>
										<animateMotion dur="10s" begin="6.2700000000000005s" repeatCount="indefinite"
											rotate="auto">
											<mpath xlink:href="#shopPath"></mpath>
										</animateMotion>
									</g>
									<g class="arrow a21">
										<path d="M 0 0 L 8 8 L0 16" fill="none" stroke="#fff" stroke-opacity="0.5"
											stroke-width="3" stroke-linecap="round" stroke-linejoin="round"
											style="transform:translateY(-8px)"></path>
										<animateMotion dur="10s" begin="6.6000000000000005s" repeatCount="indefinite"
											rotate="auto">
											<mpath xlink:href="#shopPath"></mpath>
										</animateMotion>
									</g>
									<g class="arrow a22">
										<path d="M 0 0 L 8 8 L0 16" fill="none" stroke="#fff" stroke-opacity="0.5"
											stroke-width="3" stroke-linecap="round" stroke-linejoin="round"
											style="transform:translateY(-8px)"></path>
										<animateMotion dur="10s" begin="6.930000000000001s" repeatCount="indefinite"
											rotate="auto">
											<mpath xlink:href="#shopPath"></mpath>
										</animateMotion>
									</g>
									<g class="arrow a23">
										<path d="M 0 0 L 8 8 L0 16" fill="none" stroke="#fff" stroke-opacity="0.5"
											stroke-width="3" stroke-linecap="round" stroke-linejoin="round"
											style="transform:translateY(-8px)"></path>
										<animateMotion dur="10s" begin="7.260000000000001s" repeatCount="indefinite"
											rotate="auto">
											<mpath xlink:href="#shopPath"></mpath>
										</animateMotion>
									</g>
									<g class="arrow a24">
										<path d="M 0 0 L 8 8 L0 16" fill="none" stroke="#fff" stroke-opacity="0.5"
											stroke-width="3" stroke-linecap="round" stroke-linejoin="round"
											style="transform:translateY(-8px)"></path>
										<animateMotion dur="10s" begin="7.590000000000001s" repeatCount="indefinite"
											rotate="auto">
											<mpath xlink:href="#shopPath"></mpath>
										</animateMotion>
									</g>
									<g class="arrow a25">
										<path d="M 0 0 L 8 8 L0 16" fill="none" stroke="#fff" stroke-opacity="0.5"
											stroke-width="3" stroke-linecap="round" stroke-linejoin="round"
											style="transform:translateY(-8px)"></path>
										<animateMotion dur="10s" begin="7.92s" repeatCount="indefinite" rotate="auto">
											<mpath xlink:href="#shopPath"></mpath>
										</animateMotion>
									</g>
									<g class="arrow a26">
										<path d="M 0 0 L 8 8 L0 16" fill="none" stroke="#fff" stroke-opacity="0.5"
											stroke-width="3" stroke-linecap="round" stroke-linejoin="round"
											style="transform:translateY(-8px)"></path>
										<animateMotion dur="10s" begin="8.25s" repeatCount="indefinite" rotate="auto">
											<mpath xlink:href="#shopPath"></mpath>
										</animateMotion>
									</g>
									<g class="arrow a27">
										<path d="M 0 0 L 8 8 L0 16" fill="none" stroke="#fff" stroke-opacity="0.5"
											stroke-width="3" stroke-linecap="round" stroke-linejoin="round"
											style="transform:translateY(-8px)"></path>
										<animateMotion dur="10s" begin="8.58s" repeatCount="indefinite" rotate="auto">
											<mpath xlink:href="#shopPath"></mpath>
										</animateMotion>
									</g>
									<g class="arrow a28">
										<path d="M 0 0 L 8 8 L0 16" fill="none" stroke="#fff" stroke-opacity="0.5"
											stroke-width="3" stroke-linecap="round" stroke-linejoin="round"
											style="transform:translateY(-8px)"></path>
										<animateMotion dur="10s" begin="8.91s" repeatCount="indefinite" rotate="auto">
											<mpath xlink:href="#shopPath"></mpath>
										</animateMotion>
									</g>
									<g class="arrow a29">
										<path d="M 0 0 L 8 8 L0 16" fill="none" stroke="#fff" stroke-opacity="0.5"
											stroke-width="3" stroke-linecap="round" stroke-linejoin="round"
											style="transform:translateY(-8px)"></path>
										<animateMotion dur="10s" begin="9.24s" repeatCount="indefinite" rotate="auto">
											<mpath xlink:href="#shopPath"></mpath>
										</animateMotion>
									</g>
									<g class="arrow a30">
										<path d="M 0 0 L 8 8 L0 16" fill="none" stroke="#fff" stroke-opacity="0.5"
											stroke-width="3" stroke-linecap="round" stroke-linejoin="round"
											style="transform:translateY(-8px)"></path>
										<animateMotion dur="10s" begin="9.57s" repeatCount="indefinite" rotate="auto">
											<mpath xlink:href="#shopPath"></mpath>
										</animateMotion>
									</g>
								</g>
							</svg>
						</view>
						<!-- 购物流程节点 start -->
						<ul class="pshop-nodes" id="mstValueIcos" style="list-style-type: none;display: none;">
							<li class="item item1">
								<view class="box">
									<view class="txtbox">
										<view class="tit">
											Discover Short Films/High-Quality Video
										</view>
									</view>
								</view>
							</li>
							<li class="item item2">
								<view class="box">
									<view class="txtbox">
										<view class="tit">
											Discover Short Films/High-Quality Video
										</view>
									</view>
								</view>
							</li>
							<li class="item item3">
								<view class="box">
									<view class="txtbox">
										<view class="tit">
											Discover Short Films/High-Quality Video
										</view>
									</view>
								</view>
							</li>
							<li class="item item4">
								<view class="box">
									<view class="txtbox">
										<view class="tit">
											Discover Short Films/High-Quality Video
										</view>
									</view>
								</view>
							</li>
						</ul>

					</view>
					<view v-if="ispc === false" class="app_dri_boxbody">
						<view class="app_dri_box">
							<view class="dritit">
								Discover Great Shows
							</view>
							<view class="drimes">
								Personalized recommendations help users explore trending short dramas, fun challenges,
								and
								celebrity content. Daily updates ensure everyone finds content they love.
							</view>
						</view>
						<view class="app_dri_box">
							<view class="dritit">
								User Engagement
							</view>
							<view class="drimes">
								Earn Mi Beans by watching shows, completing tasks, and interacting. The more active you
								are,
								the higher your rewards and level.
							</view>
						</view>
						<view class="app_dri_box">
							<view class="dritit">
								Viral Growth
							</view>
							<view class="drimes">
								Share invite codes or team up to spread the platform. Turn viewers into promoters for
								organic growth.
							</view>
						</view>
						<view class="app_dri_box">
							<view class="dritit">
								Boost Retention
							</view>
							<view class="drimes">
								Daily Mi Bean rewards, level badges, and time-limited tasks keep users active. Build a
								growth loop: content → incentives → sharing → new content.
							</view>
						</view>
					</view>
				</view>
			</view>
			<view class="platform_boxbody" style="overflow: hidden;">
				<view class="pla_top_mesbox">
					<view v-if="ispc" class="" style="color: #BAFC51;">
						<!-- Creators Flooding the Platform -->
						{{$t('platform.tit1')}}
					</view>
					<view v-else class="" style="color: #BAFC51;display: inline;">
						{{$t('platform.tit1')}}
					</view>
					{{$t('platform.tit2')}}
				</view>
				<view class="pla_img_box" v-if="ispc===true">
					<!-- <image src="../../static/image/Frame.png" mode=""></image> -->
					<!-- web端瀑布流 start -->
					<view class="mstValue1-imgsbox hidden-lg-down" style=" height: 1000px;"
						@mousewheel.stop="handleMouseWheel2">
						<view class="mstValue1-imgs">
							<view class="row">
								<!-- imgslist start -->
								<view class="imgslist list1" id="valueImgList1"
									:style="{'transform':`translateY(${trans1}%)`}">
									<view class="item">
										<ul class="item-list">
											<li>
												<view class="box">
													<image
														src="https://www.tiktokforbusinessoutbound.com/img/market/value/1.png"
														mode="widthFix"></image>
												</view>
											</li>
											<li>
												<view class="box">
													<image
														src="https://www.tiktokforbusinessoutbound.com/img/market/value/2.png"
														mode="widthFix"></image>
												</view>
											</li>
										</ul>
										<view class="item-tit">
											#TipsBelanja
										</view>
									</view>
									<view class="item">
										<ul class="item-list">
											<li>
												<view class="box">
													<image
														src="https://www.tiktokforbusinessoutbound.com/img/market/value/3.png"
														mode="widthFix"></image>
												</view>
											</li>
										</ul>
									</view>
								</view>
								<view class="imgslist list1" id="valueImgList1"
									:style="{'transform':`translateY(${trans2}%)`}">
									<view class="item">
										<ul class="item-list">
											<li>
												<view class="box">
													<image
														src="https://www.tiktokforbusinessoutbound.com/img/market/value/4.png"
														mode="widthFix"></image>
												</view>
											</li>
											<li>
												<view class="box">
													<image
														src="https://www.tiktokforbusinessoutbound.com/img/market/value/5.png"
														mode="widthFix"></image>
												</view>
											</li>
										</ul>
										<view class="item-tit">
											#BagikanDiskon
										</view>
									</view>
									<view class="item">
										<ul class="item-list">
											<li>
												<view class="box">
													<image
														src="https://www.tiktokforbusinessoutbound.com/img/market/value/6.png"
														mode="widthFix"></image>
												</view>
											</li>
										</ul>
									</view>
								</view>
								<view class="imgslist list1" id="valueImgList1"
									:style="{'transform':`translateY(${trans3}%)`}">
									<view class="item">
										<ul class="item-list">
											<li>
												<view class="box">
													<image
														src="https://www.tiktokforbusinessoutbound.com/img/market/value/7.png"
														mode="widthFix"></image>
												</view>
											</li>
											<li>
												<view class="box">
													<image
														src="https://www.tiktokforbusinessoutbound.com/img/market/value/8.png"
														mode="widthFix"></image>
												</view>
											</li>
										</ul>
										<view class="item-tit">
											#UnboxingOOTD
										</view>
									</view>
									<view class="item">
										<ul class="item-list">
											<li>
												<view class="box">
													<image
														src="https://www.tiktokforbusinessoutbound.com/img/market/value/9.png"
														mode="widthFix"></image>
												</view>
											</li>
										</ul>
									</view>
								</view>
								<view class="imgslist list1" id="valueImgList1"
									:style="{'transform':`translateY(${trans4}%)`}">
									<view class="item">
										<ul class="item-list">
											<li>
												<view class="box">
													<image
														src="https://www.tiktokforbusinessoutbound.com/img/market/value/10.png"
														mode="widthFix"></image>
												</view>
											</li>
											<li>
												<view class="box">
													<image
														src="https://www.tiktokforbusinessoutbound.com/img/market/value/11.png"
														mode="widthFix"></image>
												</view>
											</li>
										</ul>
										<view class="item-tit">
											#RekomendasiProduk
										</view>
									</view>
									<view class="item">
										<ul class="item-list">
											<li>
												<view class="box">
													<image
														src="https://www.tiktokforbusinessoutbound.com/img/market/value/12.png"
														mode="widthFix"></image>
												</view>
											</li>
										</ul>
									</view>
								</view>
							</view>
						</view>
					</view>
				</view>
				<view v-else class="app_img_box">
					<view class="leftimgbox">
						<image src="../../static/image/List (3).png" mode=""></image>
					</view>
					<view class="rightimgbox">
						<image src="../../static/image/List (2).png" mode=""></image>
					</view>
				</view>
			</view>
			<view class="partners_boxbody">
				<view class="partners_box">
					<image v-if="ispc === true" src="../../static/image/div.investor.png" mode=""></image>
					<image v-else src="../../static/image/div.investor1.png" mode="widthFix"></image>
				</view>
			</view>
			<view class="team_boxbody">
				<view class="team_box_body">
					<view class="t_top_tit">
						<view class="" style="color: #BAFC51;display: inline;">
							{{$t('team.tit1')}}
						</view> {{$t('team.tit2')}}
					</view>
					<view class="t_top_mes">
						{{$t('team.tit3')}}
					</view>
					<view v-if="ispc===true" class="t_bot_tabbox_body">
						<view class="t_tab_box">
							<view class="tab_top_userbox">
								<view class="userimg">
									<image src="../../static/image/team/img4.png.png" mode=""></image>
								</view>
								<view class="usermes">
									<view class="name">
										{{$t('team.teamtab[0].name')}}
									</view>
									<view class="zw">
										{{$t('team.teamtab[0].job')}}
									</view>
								</view>
							</view>
							<view class="tab_mes_box">
								{{$t('team.teamtab[0].mes1')}}
							</view>
							<view class="tab_keywords">
								{{$t('team.teamtab[0].mes2')}}
							</view>
							<view class="tab_follower">
								<view class="followermes">
									Followers: 762K
								</view>
								<view class="followerimg">
									<image src="../../static/image/team/SVG (1).png" mode=""></image>
								</view>
							</view>
						</view>
						<view class="t_tab_box">
							<view class="tab_top_userbox">
								<view class="userimg">
									<image src="../../static/image/team/img4.png (1).png" mode=""></image>
								</view>
								<view class="usermes">
									<view class="name">
										{{$t('team.teamtab[1].name')}}
									</view>
									<view class="zw">
										{{$t('team.teamtab[1].job')}}
									</view>
								</view>
							</view>
							<view class="tab_mes_box">
								{{$t('team.teamtab[1].mes1')}}
							</view>
							<view class="tab_keywords">
								{{$t('team.teamtab[1].mes2')}}
							</view>
							<view class="tab_follower">
								<view class="followermes">
									Followers: 55k
								</view>
								<view class="followerimg">
									<image src="../../static/image/team/SVG (1).png" mode=""></image>
								</view>
							</view>
						</view>
						<view class="t_tab_box">
							<view class="tab_top_userbox">
								<view class="userimg">
									<image src="../../static/image/team/img4.png (2).png" mode=""></image>
								</view>
								<view class="usermes">
									<view class="name">
										{{$t('team.teamtab[2].name')}}
									</view>
									<view class="zw">
										{{$t('team.teamtab[2].job')}}
									</view>
								</view>
							</view>
							<view class="tab_mes_box">
								{{$t('team.teamtab[2].mes1')}}
							</view>
							<view class="tab_keywords">
								{{$t('team.teamtab[2].mes2')}}
							</view>
							<view class="tab_follower">
								<view class="followermes">
									Followers: 85k
								</view>
								<view class="followerimg">
									<image src="../../static/image/team/SVG (1).png" mode=""></image>
								</view>
							</view>
						</view>
						<view class="t_tab_box">
							<view class="tab_top_userbox">
								<view class="userimg">
									<image src="../../static/image/team/img4.png (3).png" mode=""></image>
								</view>
								<view class="usermes">
									<view class="name">
										{{$t('team.teamtab[3].name')}}
									</view>
									<view class="zw">
										{{$t('team.teamtab[3].job')}}
									</view>
								</view>
							</view>
							<view class="tab_mes_box">
								{{$t('team.teamtab[3].mes1')}}
							</view>
							<view class="tab_keywords">
								{{$t('team.teamtab[3].mes2')}}
							</view>
							<view class="tab_follower">
								<view class="followermes">
									Followers: 192K
								</view>
								<view class="followerimg">
									<image src="../../static/image/team/SVG (1).png" mode=""></image>
								</view>
							</view>
						</view>
					</view>
					<view v-else class="t_bot_tabbox_body">
						<scroll-view class="botsscroll" scroll-x="true" style="white-space: nowrap;">
							<view class="t_tab_box">
								<view class="tab_top_userbox">
									<view class="userimg">
										<image src="../../static/image/team/img4.png.png" mode=""></image>
									</view>
									<view class="usermes">
										<view class="name">
											Ethan
										</view>
										<view class="zw">
											CEO
										</view>
									</view>
								</view>
								<view class="tab_mes_box">
									Harvard PhD, former Apple Strategic Investment Director, co-founder of a Web3
									startup.
									Experienced in content platform funding. Founded Funshot to create a decentralized
									content
									ecosystem where everyone participates and benefits.
								</view>
								<view class="tab_keywords">
									Keywords: Platform Strategy / Incentive Model / Web3 Content Economy
								</view>
								<view class="tab_follower">
									<view class="followermes">
										Followers: 762K
									</view>
									<view class="followerimg">
										<image src="../../static/image/team/SVG (1).png" mode=""></image>
									</view>
								</view>
							</view>
							<view class="t_tab_box">
								<view class="tab_top_userbox">
									<view class="userimg">
										<image src="../../static/image/team/img4.png (1).png" mode=""></image>
									</view>
									<view class="usermes">
										<view class="name">
											Ken
										</view>
										<view class="zw">
											CTO
										</view>
									</view>
								</view>
								<view class="tab_mes_box">
									MIT Computer Science graduate, former ByteDance tech architect. Expert in blockchain
									architecture and high concurrency systems. Designed Funshot F-Bean incentive
									mechanism
									and
									growth model.
								</view>
								<view class="tab_keywords">
									Keywords: Web3 System Design / Deflationary Mechanism / Distributed Computing
									Architecture
								</view>
								<view class="tab_follower">
									<view class="followermes">
										Followers: 55k
									</view>
									<view class="followerimg">
										<image src="../../static/image/team/SVG (1).png" mode=""></image>
									</view>
								</view>
							</view>
							<view class="t_tab_box">
								<view class="tab_top_userbox">
									<view class="userimg">
										<image src="../../static/image/team/img4.png (2).png" mode=""></image>
									</view>
									<view class="usermes">
										<view class="name">
											Lisa
										</view>
										<view class="zw">
											CCO
										</view>
									</view>
								</view>
								<view class="tab_mes_box">
									Yale Journalism and Communication graduate, former TikTok International Content
									Operations
									Director. Led growth strategies for TikTok drama channels. Skilled in content
									matrix,
									storytelling, and incentive mechanisms.
								</view>
								<view class="tab_keywords">
									Keywords: Drama Content Planning / Global Creator Ecosystem / Content
									Commercialization
								</view>
								<view class="tab_follower">
									<view class="followermes">
										Followers: 85k
									</view>
									<view class="followerimg">
										<image src="../../static/image/team/SVG (1).png" mode=""></image>
									</view>
								</view>
							</view>
							<view class="t_tab_box">
								<view class="tab_top_userbox">
									<view class="userimg">
										<image src="../../static/image/team/img4.png (3).png" mode=""></image>
									</view>
									<view class="usermes">
										<view class="name">
											Alex
										</view>
										<view class="zw">
											CGO
										</view>
									</view>
								</view>
								<view class="tab_mes_box">
									Stanford Marketing and Media Management graduate, former TikTok for Business Global
									Partnerships VP. Managed Funshot × Neymar global endorsement and multi-country
									market
									launches.
								</view>
								<view class="tab_keywords">
									Keywords: Global Strategy / Celebrity Collaboration / Channel Expansion & BD
								</view>
								<view class="tab_follower">
									<view class="followermes">
										Followers: 192K
									</view>
									<view class="followerimg">
										<image src="../../static/image/team/SVG (1).png" mode=""></image>
									</view>
								</view>
							</view>
						</scroll-view>
					</view>
				</view>
			</view>
			<view class="bottom_box_body">
				<view v-if="ispc" class="top_concactus_boxbody">
					<view class="concactus_box_body">
						<view class="c_toptit">
							{{$t('Support')}}
						</view>
						<view class="c_topmes">
							{{$t('ecosystem')}}
						</view>
						<view class="c_an" @click="gotowburl">
							{{$t('ContactUs')}}
						</view>
					</view>
				</view>
				<view class="bot_concactus_boxbody">
					<!-- <image v-if="ispc === true" src="../../static/image/Footer.png" mode=""></image> -->
					<view v-if="ispc === true" class="bottom_joinbo">
						<!-- <image src="../../static/image/Footer.png" mode=""></image> -->
						<view class="dh_menu_boxbody">
							<view class="dhtit1">
								{{$t('dhmenu[0].tit')}}
							</view>
							<view class="dhtit2">
								{{$t('dhmenu[0].mes1')}}
							</view>
							<view class="dhtit2">
								{{$t('dhmenu[0].mes2')}}
							</view>
						</view>
						<view class="dh_menu_boxbody">
							<view class="dhtit1">
								{{$t('dhmenu[1].tit')}}
							</view>
							<view class="dhtit2">
								{{$t('dhmenu[1].mes1')}}
							</view>
							<view class="dhtit2">
								{{$t('dhmenu[1].mes2')}}
							</view>
						</view>
						<view class="dh_menu_boxbody">
							<view class="dhtit1">
								{{$t('FollowUs')}}
							</view>
							<view class="dhiconboxbody">
								<view class="dhicon" style="margin-right: 17px;" @click="gotowburl">
									<image src="../../static/image/join/Item.png" mode=""></image>
								</view>
								<view class="dhicon" @click="gotowburl">
									<image src="../../static/image/join/Item (1).png" mode=""></image>
								</view>
							</view>
						</view>
						<view class="Subscribe_boxbody">
							<view class="Subscribe_topbox">
								{{$t('dhmenu[2].tit')}}
								<view class="sub_box">
									{{$t('dhmenu[2].mes1')}}
								</view>
							</view>
							<view class="subtit">
								{{$t('dhmenu[2].mes2')}}
							</view>
						</view>
					</view>
					<image v-else src="../../static/image/Footer1.png" mode=""></image>
				</view>
			</view>
		</scroll-view>
	</view>
	<view v-else class="content wrapper index_body" style="padding-top: 0;">
		<!-- <p style="color: white;">{{ $t('message.hello') }}</p> -->
		<header class="header" v-if="ispc === true">
			<view class="header_box">
				<view class="logo_appname_boxbody">
					<view class="logo_appname_box">
						<view class="logo_box">
							<image src="../../static/image/logo3x.png" mode=""></image>
						</view>
						<view class="appname_box">
							<view class="appname">
								{{ $t('logotit') }}
							</view>
							<view class="log_tit">
								{{ $t('logotitmes') }}
							</view>
						</view>
					</view>
				</view>
				<view class="header_menu_boxbody">
					<view class="header_menu_box" :class="menuactive === index ? 'active' : ''"
						v-for="(item,index) in menulist" :key="index" @click="menua(index)">
						<view class="menu_tit">
							<view v-if="item !== 'Download'" class="">
								{{$t('header.' + item)}}
							</view>
							<view v-else class="hot_download">
								<view class="hotimg">
									<image src="../../static/image/Vector.png" mode=""></image>
								</view>
								<view class="">
									{{$t('header.' + item)}}
								</view>
							</view>
						</view>
						<view class="a_liner">

						</view>
					</view>
				</view>
				<view class="lang_contact_box">
					<view class="lang_box_body">
						<view class="lang_imgbox">
							<image src="../../static/image/SVG.png" mode=""></image>
						</view>
						<view class="lang_choose_boxbody" @click="showlangchange">
							<view class="lang_mes">
								{{$t(lan)}}
							</view>
							<view class="iconfont icon-xiala xiala">

							</view>
						</view>
						<view class="langchange_box_body">
							<view class="lang_box" v-for="(item,index) in langlist" :key="index"
								@click="lanchange(item)">
								{{$t('lang.' + item)}}
							</view>
						</view>
					</view>
					<view class="contact_box" @click="gotowburl">
						<view class="contxt">
							{{$t('ContactUs')}}
						</view>
					</view>
				</view>
			</view>
		</header>
		<view v-else class="appheader">
			<view class="left_apphaderlogo">
				<view class="logo_appbox">
					<image src="../../static/image/logo3x.png" mode=""></image>
				</view>
				<view class="appname_box">
					<view class="appname_t">
						{{$t('logotit')}}
					</view>
					<view class="appname_b">
						{{$t('logotitmes')}}
					</view>
				</view>
			</view>
			<view class="hotapp_box" @click="gotopage">
				<image src="../../static/image/Group 1533208842.png" mode=""></image>
			</view>
		</view>
		<!-- 			<view v-show="langch" class="langchange_box_body">
				<view class="lang_box" v-for="(item,index) in langlist" :key="index" @click="lanchange(item)">
					{{$t('lang.' + item)}}
				</view>
			</view> -->
		<view class="pc_banner_boxbody">
			<swiper class="spswiper swiper-box" style="height: 100%;" circular :indicator-dots="ispc"
				indicator-color="rgba(255, 255, 255, 1)" :autoplay="true" :interval="2000" :duration="500">
				<swiper-item>
					<view v-if="ispc === true" class="banner_boxbody">
						<view class="top_tit_mes_box" style="position: absolute;top: 0;z-index: 999;padding-top: 0;">
							<view class="top_tit_mes">
								<view class="top_tit">
									{{$t('pcBanner.banner1.Incentive')}}
									<view class="" style="color: #BAFC51;display: inline">
										{{$t('pcBanner.banner1.Worldwide')}}
									</view>
								</view>
								<view class="mes_box">
									{{$t('pcBanner.banner1.msg')}}
								</view>
							</view>
						</view>
						<view class="banner_swiper_boxbody">
							<image src="../../static/image/banner/图层 11asdsa 1.png" mode="widthFix"></image>
						</view>
					</view>
					<view v-else class="banner_boxbody">
						<view class="top_tit_mes_box">
							<view class="top_tit_mes">
								<view class="top_tit">
									{{$t('pcBanner.banner1.Incentive')}}
									<view class="" style="color: #BAFC51;display: inline">
										{{$t('pcBanner.banner1.Worldwide')}}
									</view>
								</view>
								<view class="mes_box">
									{{$t('pcBanner.banner1.msg')}}
								</view>
							</view>
						</view>
						<view class="banner_swiper_boxbody">
							<image src="../../static/image/banner/图层 11asdsa 1.png" mode="widthFix"></image>
						</view>
					</view>
				</swiper-item>
				<swiper-item>
					<view class="banner_boxbody" v-if="ispc === true">
						<view class="banner_swiper_boxbody">
							<image src="../../static/image/banner/图层 1 拷s2daa贝.png" mode="widthFix"></image>
						</view>
						<view class="top_tit_mes_box"
							style="position: absolute;bottom: 0;z-index: 999;padding-top: 160rpx;">
							<view class="top_tit_mes">
								<view class="top_tit">
									{{$t('pcBanner.banner2.Incentive')}}
									<view class="" style="color: #BAFC51;display: inline">
										{{$t('pcBanner.banner2.Worldwide')}}
									</view>
								</view>
								<view class="mes_box">
									{{$t('pcBanner.banner2.msg')}}
								</view>
							</view>
						</view>
					</view>
					<view v-else class="banner_boxbody">
						<view class="top_tit_mes_box">
							<view class="top_tit_mes">
								<view class="top_tit">
									{{$t('pcBanner.banner2.Incentive')}}
									<view class="" style="color: #BAFC51;display: inline">
										{{$t('pcBanner.banner2.Worldwide')}}
									</view>
								</view>
								<view class="mes_box">
									{{$t('pcBanner.banner2.msg')}}
								</view>
							</view>
						</view>
						<view class="banner_swiper_boxbody">
							<image src="../../static/image/banner/图层 1 拷s2daa贝.png" mode="widthFix"></image>
						</view>
					</view>
				</swiper-item>
				<swiper-item>
					<view class="banner_boxbody" v-if="ispc === true">
						<view class="banner_swiper_boxbody_b3">
							<image src="../../static/image/banner/图层 1dsa 1.png" mode="widthFix"></image>
							<view class="tit_mes_zinx">
								<view class="blogo_boxbody">
									<image src="../../static/image/banner/Group 1533213357.png" mode=""></image>
								</view>
								<view class="bold_tit_box">
									{{$t('pcBanner.banner3.First')}}
									<view style="color: #BAFC51;display: inline">{{$t('pcBanner.banner3.Watch')}}
									</view>
									{{$t('pcBanner.banner3.Platform')}}
								</view>
								<view class="b3txt">
									{{$t('pcBanner.banner3.msg')}}
								</view>
							</view>
						</view>
					</view>
					<view v-else class="banner_boxbody">
						<view class="top_tit_mes_box">
							<view class="top_tit_mes">
								<view class="top_tit">
									{{$t('pcBanner.banner3.First')}}
									<view style="color: #BAFC51;display: inline">{{$t('pcBanner.banner3.Watch')}}
									</view>
									{{$t('pcBanner.banner3.Platform')}}
								</view>
								<view class="mes_box">
									{{$t('pcBanner.banner3.msg')}}
								</view>
							</view>
						</view>
						<view class="banner_swiper_boxbody">
							<image src="../../static/image/banner/图层 1dsa 1.png" mode="widthFix"></image>
						</view>
					</view>
				</swiper-item>
				<swiper-item>
					<view class="banner_boxbody" v-if="ispc === true">
						<view class="banner_swiper_boxbody_b3">
							<image src="../../static/image/banner/图层 1dsad.png" mode="widthFix"></image>
							<view class="tit_mes_zinx">
								<view class="blogo_boxbody">
									<image src="../../static/image/banner/Group 1533213357.png" mode=""></image>
								</view>
								<view class="bold_tit_box">
									<view style="color: #BAFC51;display: inline">{{$t('pcBanner.banner4.Why')}}
									</view>
									{{$t('pcBanner.banner4.FunShot')}}
								</view>
								<view class="b3txt">
									{{$t('pcBanner.banner4.msg')}}
								</view>
							</view>
						</view>
					</view>
					<view v-else class="banner_boxbody">
						<view class="top_tit_mes_box">
							<view class="top_tit_mes">
								<view class="top_tit">
									<view style="color: #BAFC51;display: inline">{{$t('pcBanner.banner4.Why')}}
									</view>
									{{$t('pcBanner.banner4.FunShot')}}
								</view>
								<view class="mes_box">
									{{$t('pcBanner.banner4.msg')}}
								</view>
							</view>
						</view>
						<view class="banner_swiper_boxbody">
							<image src="../../static/image/banner/图层 1dsad.png" mode="widthFix"></image>
						</view>
					</view>
				</swiper-item>
			</swiper>
			<view class="hmban-mouse-wrap hidden-lg-down is-light">
				<view class="hmban-mouse">
					<view class="dw d1"></view>
					<view class="dw d2"></view>
				</view>
			</view>
		</view>
		<view id="Overview" class="upgrades_boxbody">
			<view class="upgrades_box_body">
				<view class="upgrades_bold_tit">
					<view class="u_bold_t">
						<view class="" style="color: #BAFC51;display: inline">
							{{$t('upgrades.tit1')}}
						</view> {{$t('upgrades.tit2')}}
					</view>
					<view class="u_m_t">
						{{$t('upgrades.tit3')}}
					</view>
				</view>
				<view class="upgrades_img_mes_boxbody">
					<view class="left_chose_boxbody">
						<view class="left_chose_box" :class="leftchoseinx === index ? 'active' : ''"
							v-for="(item,index) in leftchose" :key="index" @click="leftcho(index)"
							@mousewheel.stop="handleMouseWheel">
							<!-- {{item.ltit}} -->
							{{$t('upgrades.leftchose[' + index + '].ltit')}}
						</view>
					</view>
					<view v-if="ispc === true" class="right_bo_mes_boxbody" @mousewheel.stop="handleMouseWheel">
						<swiper class="swiper" :current="leftchoseinx" :vertical="ispc" circular interval="5000"
							duration="500">
							<swiper-item v-for="(item,index) in leftchose" :key="index">
								<view class="spitem" :class="leftchoseinx === index ? 'active' : ''">
									<view class="bo_left_img_box">
										<image :src="item.imgurl" mode=""></image>
									</view>
									<view class="bo_right_mes_box_body">
										<view class="mes_box_body">
											<view class="mes_box_tit">
												<!-- {{item.rtit}} -->
												{{$t('upgrades.leftchose[' + index + '].rtit')}}
											</view>
											<view class="mes_box_mes" style="white-space: pre-line;">
												<!-- {{item.rmes}} -->
												{{$t('upgrades.leftchose[' + index + '].rmes')}}
											</view>
										</view>
										<view class="topview_bm_box_body">
											<view class="topview_bm_box">
												<view class="tb" :class="tbinx === index ? 'active' : ''"
													v-for="(item,index) in tb" :key="index" @click="tbclick(index)">
													{{item}}
												</view>
											</view>
											<view class="anniu">
												<image src="../../static/image/Button - Next slide.png" mode="">
												</image>
											</view>
										</view>
									</view>
								</view>
							</swiper-item>

						</swiper>

					</view>
					<view v-else class="aas" style="width: 100%;margin-top: 40rpx;">
						<swiper class="swiper" :current="leftchoseinx" :vertical="ispc" circular interval="5000"
							duration="500" @change="currentchange">
							<swiper-item v-for="(item,index) in leftchose" :key="index">
								<view class="right_bo_mes_boxbody">
									<view class="bo_left_img_box">
										<image :src="item.imgurl" mode=""></image>
									</view>
									<view class="bo_right_mes_box_body">
										<view class="mes_box_body">
											<view class="mes_box_tit">
												<!-- {{item.rtit}} -->
												{{$t('upgrades.leftchose[' + index + '].rtit')}}
											</view>
											<view class="mes_box_mes" style="white-space: pre-line;">
												<!-- {{item.rmes}} -->
												{{$t('upgrades.leftchose[' + index + '].rmes')}}
											</view>
										</view>
										<view class="topview_bm_box_body">
											<view class="topview_bm_box">
												<view class="tb" :class="tbinx === index ? 'active' : ''"
													v-for="(item,index) in tb" :key="index" @click="tbclick(index)">
													{{item}}
												</view>
											</view>
											<view class="anniu">
												<image src="../../static/image/Button - Next slide.png" mode="">
												</image>
											</view>
										</view>
									</view>
								</view>

							</swiper-item>

						</swiper>
					</view>
				</view>
			</view>
		</view>
		<view id="Performance" class="funshot_box_body">
			<view class="funshot_boxbody">
				<view class="funshot_toptit">
					<view v-if="ispc=== true" class="" style="color: #BAFC51;display: inline">
						{{$t('funshot.tit1')}}
					</view>
					<view v-else class="" style="color: #BAFC51;">
						{{$t('funshot.tit1')}}
					</view>
					{{$t('funshot.tit2')}}
				</view>
				<view class="funshot_bot_mesboxbody">
					<view class="funshotb_left">
						<view class="flf_box" v-for="(item,index) in flfdata" :key="index" @click="flfclick(index)"
							@mousewheel.stop="handleMouseWheel1">
							<view class="flf_b1" v-if="flfinx !== index">
								<!-- {{item.tit}} -->
								{{$t('funshot.flfdata[' + index + '].tit')}}
							</view>
							<view v-else class="flf_b2">
								<view class="flf_b2_tit">
									<!-- {{item.tit}} -->
									{{$t('funshot.flfdata[' + index + '].tit')}}
								</view>
								<view class="flf_b2_mes">
									<!-- {{item.mes}} -->
									{{$t('funshot.flfdata[' + index + '].mes')}}
								</view>
							</view>
						</view>
					</view>
					<view class="app_funshotb_boxbody" v-show="ispc === false">
						<view class="app_fbimgbox">
							<video id="myVideo" src="../../static/image/51593_1745628702_raw.mp4" :loop="true"
								:controls="true"></video>
						</view>
						<swiper class="swiper" style="height: 176rpx;" :current="flfinx" :vertical="true" circular
							interval="5000" duration="500">
							<swiper-item v-for="(item,index) in flfdata" :key="index">
								<view class="app_fbsjbox">
									<view class="to_fbsjb">
										<view class="l_fbsjb">
											{{item.num}}
										</view>
										<view class="l_fbsmes">
											#Funshot Platform users have accumulated breakthroughs (as of April
											2025)
										</view>
									</view>
									<view class="fh">
										{{item.dw}}
									</view>
								</view>
							</swiper-item>

						</swiper>

					</view>
					<view class="funshotb_cen" v-show="ispc">
						<!-- <image src="../../static/image/Group 1533213380.png" mode=""></image> -->
						<video src="../../static/image/51593_1745628702_raw.mp4" :loop="true" :controls="true"></video>
					</view>
					<view class="ccswiper" v-show="ispc" style="flex: 1;height: 100%;margin-left: 40rpx;"
						@mousewheel.stop="handleMouseWheel1">

						<swiper class="swiper" :current="flfinx" :vertical="true" circular interval="5000"
							duration="500">
							<swiper-item v-for="(item,index) in flfdata" :key="index">
								<view class="funshotb_right" :class="flfinx === index ? 'active' : ''">
									<view class="num_box_body">
										<view class="num_box">
											{{item.num}}
										</view>
										<view class="l_mes">
											· Over 15.87 million users as of April 2025
										</view>
									</view>
									<view class="dw_box">
										{{item.dw}}
									</view>
								</view>
							</swiper-item>

						</swiper>

					</view>

				</view>
			</view>
		</view>
		<view class="aliexpress_box_body">
			<view class="aliexpress_boxbody">
				<view class="left_ali_tab_boxbody">
					<view class="ali_tab_boxbody" :class="aliinx === index ? 'active' : ''"
						v-for="(item,index) in alitab" :key="index" @click="aliclick(index)">
						<image v-if="ispc === true" :src="item.url" mode="heightFix" class="alitabimg"></image>
						<image v-else :src="item.svg" mode="" class="alitabimg" style="width: 100%;">
						</image>
					</view>
				</view>

				<swiper v-if="ispc === true" class="swiper" style="width: 1300rpx;" :current="aliinx" :vertical="true"
					circular interval="5000" duration="500">
					<swiper-item v-for="(item,index) in alitab" :key="index">
						<!-- <image v-if="ispc === true" src="../../static/image/Container.png" mode="heightFix"></image> -->
						<view class="right_ali_imgboxbody" style="width: 620px;display: flex;flex-direction: column;">
							<!-- 								<image v-if="ispc === true" src="../../static/image/Container.png" mode="heightFix">
								</image>
								<image v-else src="../../static/image/Container.png" mode=""></image> -->
							<view class="yinhao" style="width: 36px;height: 28px;">
								<image src="../../static/image/bi_quote.svg.png" mode=""></image>
							</view>
							<view class="rmesg"
								style="box-sizing: border-box;padding-left: 60px;width: 100%;height: auto;font-size: 26px;">
								{{$t('aliexpress.tit1')}}
							</view>
							<view class=""
								style="box-sizing: border-box;padding-left: 60px;width: 100%;height: auto;font-size: 16px;margin-top: 28px;">
								{{$t('aliexpress.tit2')}}
							</view>
						</view>
					</swiper-item>

				</swiper>
				<swiper v-else class="swiper" style="width: 100%;" :current="aliinx" :vertical="true" circular
					interval="5000" duration="500">
					<swiper-item v-for="(item,index) in alitab" :key="index">
						<!-- <image v-if="ispc === true" src="../../static/image/Container.png" mode="heightFix"></image> -->
						<!-- 						<view class="right_ali_imgboxbody">
							<image v-if="ispc === true" src="../../static/image/Container.png" mode="heightFix">
							</image>
							<image v-else src="../../static/image/Container.png" mode=""></image>
						</view> -->
						<view class="right_ali_imgboxbody" style="width: 620rpx;display: flex;flex-direction: column;">
							<!-- 								<image v-if="ispc === true" src="../../static/image/Container.png" mode="heightFix">
								</image>
								<image v-else src="../../static/image/Container.png" mode=""></image> -->
							<view class="yinhao" style="width: 38rpx;height: 30rpx;">
								<image src="../../static/image/bi_quote.svg.png" mode=""></image>
							</view>
							<view class="rmesg"
								style="box-sizing: border-box;padding-left: 60px;width: 100%;height: auto;font-size: 28rpx;">
								{{$t('aliexpress.tit1')}}
							</view>
							<view class=""
								style="box-sizing: border-box;padding-left: 60px;width: 100%;height: auto;font-size: 16rpx;margin-top: 28px;">
								{{$t('aliexpress.tit2')}}
							</view>
						</view>
					</swiper-item>

				</swiper>
				<!-- 				<view class="right_ali_imgboxbody" :class="aliinx === 0 ? 'active' : ''">
							<image v-if="ispc === true" src="../../static/image/Container.png" mode="heightFix"></image>
							<image v-else src="../../static/image/Container.png" mode=""></image>
						</view>
						<view class="right_ali_imgboxbody" :class="aliinx === 1 ? 'active' : ''">
							<image v-if="ispc === true" src="../../static/image/Container.png" mode="heightFix"></image>
							<image v-else src="../../static/image/Container.png" mode=""></image>
						</view>
						<view class="right_ali_imgboxbody" :class="aliinx === 2 ? 'active' : ''">
							<image v-if="ispc === true" src="../../static/image/Container.png" mode="heightFix"></image>
							<image v-else src="../../static/image/Container.png" mode=""></image>
						</view> -->
			</view>
		</view>
		<view id="Features" class="Full-chain">
			<view class="full_chain_bodbody">
				<view class="full_toptit">
					<!-- {{fullxz[fullxzinx].toptit}} -->
					{{$t('fullxz[' + fullxzinx + '].toptit')}}
				</view>
				<view class="full_xz_boxbody">
					<!-- 						<view class="xz_box_body" :class="index === 3 ? 'xz1' : ''" v-for="(item,index) in fullxz"
							:key="index" @click="fullxzclick(index)">
							<view class="fullxz_an" :class="fullxzinx === index ? 'active' : ''">
								{{$t('fullxz[' + fullxzinx + '].xzmes')}}
							</view>
							<view class="youjiantou" v-if="item.xzmes !== 'Data Growth'">
								<image src="../../static/image/Image.png" mode=""></image>
							</view>
						</view> -->
					<view class="xz_box_body" @click="fullxzclick(0)">
						<view class="fullxz_an" :class="fullxzinx === 0 ? 'active' : ''">
							<!-- {{item.xzmes}} -->
							{{$t('fullxz[0].xzmes')}}
						</view>
						<view class="youjiantou">
							<image src="../../static/image/Image.png" mode=""></image>
						</view>
					</view>
					<view class="xz_box_body" @click="fullxzclick(1)">
						<view class="fullxz_an" :class="fullxzinx === 1 ? 'active' : ''">
							<!-- {{item.xzmes}} -->
							{{$t('fullxz[1].xzmes')}}
						</view>
						<view class="youjiantou">
							<image src="../../static/image/Image.png" mode=""></image>
						</view>
					</view>
					<view class="xz_box_body" @click="fullxzclick(2)">
						<view class="fullxz_an" :class="fullxzinx === 2 ? 'active' : ''">
							<!-- {{item.xzmes}} -->
							{{$t('fullxz[2].xzmes')}}
						</view>
						<view class="youjiantou">
							<image src="../../static/image/Image.png" mode=""></image>
						</view>
					</view>
					<view class="xz_box_body xz1" @click="fullxzclick(3)">
						<view class="fullxz_an" :class="fullxzinx === 3 ? 'active' : ''">
							<!-- {{item.xzmes}} -->
							{{$t('fullxz[3].xzmes')}}
						</view>
						<!-- 							<view class="youjiantou">
								<image src="../../static/image/Image.png" mode=""></image>
							</view> -->
					</view>
				</view>
				<view class="full_xz_botmes_boxbody">
					<view class="bot_leftimgbox" :class="fullxzinx === 2 ? 'lf1' :''">
						<image :src="fullxz[fullxzinx].url" :mode="ispc===true?'heightFix':'widthFix'"></image>
					</view>
					<view class="bot_rightmgbox">
						<view class="tit1">
							<!-- {{fullxz[fullxzinx].tit1}} -->
							{{$t('fullxz[' + fullxzinx + '].tit1')}}
						</view>
						<view class="tit2">
							<!-- {{fullxz[fullxzinx].tit2}} -->
							{{$t('fullxz[' + fullxzinx + '].tit2')}}
						</view>
						<view class="fmes">
							<!-- {{fullxz[fullxzinx].mes}} -->
							{{$t('fullxz[' + fullxzinx + '].mes')}}
						</view>
						<view class="learnmore_boxbody" v-if="fullxzinx === 0 || fullxzinx === 1">
							<view class="learnmore_box" @click="gotowburl">
								{{$t('learn')}}
							</view>
							<view class="xiangyou" v-show="ispc">
								<image src="../../static/image/Button - Next slide.png" mode=""></image>
							</view>
						</view>
						<view class="learnmore_boxbody" v-if="fullxzinx === 2">
							<view class="learnchange_boxbody">
								<view class="learnchange_box" :class="lchaninx === index ? 'active' : ''"
									v-for="(item,index) in learnchange" :key="index" @click="lanclick(index)">
									{{item}}
								</view>
							</view>
							<view class="xiangyou">
								<image src="../../static/image/Button - Next slide.png" mode=""></image>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
		<view id="Advantages" class="Ultimate_Vision">
			<view class="vision_top_mesbox">
				<view class="vt_toptit">
					<view class="" style="color: #BAFC51;display: inline">
						{{$t('Ultimate.tit1')}}
					</view> {{$t('Ultimate.tit2')}}
				</view>
				<view class="vt_botmes">
					{{$t('Ultimate.tit3')}}
				</view>
			</view>
			<view class="vision_bot_swiperboxbody">
				<view class="vision_bot_swiperbox">
					<view class="vi_limgbox">
						<image :src="viimg[currentinx]" mode=""></image>
					</view>
					<view class="vi_rswiperboxbody">
						<swiper class="spswiper1 viswpier swiper-box" style="height: 100%;" circular
							:indicator-dots="true" indicator-color="rgba(255, 255, 255, 1)" :vertical="ispc"
							:autoplay="true" :interval="2000" :duration="500" :current="currentinx"
							@change="swiperchang">
							<swiper-item v-for="i in 3" :key="i">
								<view class="vi_rswiperbox">
									<view class="vi_toptit">
										{{$t('Ultimate.vision['+ (i-1) + '].tit1')}}
									</view>
									<view class="vi_mes">
										{{$t('Ultimate.vision['+ (i-1) + '].mes')}}
									</view>
									<view class="vi_toptit">
										{{$t('Ultimate.vision['+ (i-1) + '].tit2')}}
									</view>
									<view class="vi_mes1">
										<view class="mes1_mes">
											<view class="yuandian"></view>
											<view class="mm">
												{{$t('Ultimate.vision['+(i-1) + '].mm1')}}
											</view>
										</view>
										<view class="mes1_mes">
											<view class="yuandian"></view>
											<view class="mm">
												{{$t('Ultimate.vision['+ (i-1) + '].mm2')}}
											</view>
										</view>
										<view class="mes1_mes">
											<view class="yuandian"></view>
											<view class="mm">
												{{$t('Ultimate.vision['+ (i-1) + '].mm3')}}
											</view>
										</view>
										<view class="mes1_mes">
											<view class="yuandian"></view>
											<view class="mm">
												{{$t('Ultimate.vision['+ (i-1) + '].mm4')}}
											</view>
										</view>
									</view>
								</view>
							</swiper-item>
						</swiper>

					</view>
				</view>
			</view>
		</view>
		<view class="revenue_model">
			<view class="revenue_model_boxbody">
				<view class="re_top_tit">
					<view v-if="ispc===true" class="" style="color: #BAFC51;display: inline">
						{{$t('revenue.tit1')}}
					</view>
					<view v-else class="" style="color: #BAFC51;">
						{{$t('revenue.tit1')}}
					</view>
					{{$t('revenue.tit2')}}
				</view>
				<view class="re_top_mes">
					{{$t('revenue.tit3')}}
				</view>
				<view class="diverse_boxbody pshop-path-box web hidden-lg-down">
					<!-- 背景∞图 -->
					<!-- <image src="../../static/image/Container (3).png" class="bg" mode=""></image> -->
					<image v-if="lan === 'lang.Eenglish'" src="../../static/image/Container (3).png" class="bg"
						mode="widthFix">
					</image>
					<image v-else-if="lan === 'lang.Chinese'" src="../../static/image/Container (2).png" class="bg"
						mode="widthFix"></image>
					<image v-else-if="lan === 'lang.Indonesian'" src="../../static/image/Container (4).png" class="bg"
						mode="widthFix"></image>
					<!-- 箭头svg -->
					<view class="svgbox" id="valWebSvgBox" v-if="ispc===true">
						<svg class="shopAvgWeb" id="shopVoute" x="0px" y="0px" viewBox="0 0 744 350" style="opacity: 1;"
							fill="none" xmlns="http://www.w3.org/2000/svg">
							<path id="shopPath" opacity="0" class="st0"
								d="M173.5,33.7C137,33.4,100.3,47.2,72.4,75.1l0,0c-55.2,55.2-55.2,144.6,0,199.8l0,0c55.2,55.2,144.6,55.2,199.8,0L472,75.1c55.2-55.2,144.6-55.2,199.8,0l0,0c55.2,55.2,55.2,144.6,0,199.8l0,0c-55.2,55.2-144.6,55.2-199.8,0L272.2,75.1C244.9,47.8,209.3,34,173.5,33.7">
							</path>
							<g id="shopArrow">
								<g class="arrow a1">
									<path d="M 0 0 L 8 8 L0 16" fill="none" stroke="#fff" stroke-opacity="0.5"
										stroke-width="3" stroke-linecap="round" stroke-linejoin="round"
										style="transform:translateY(-8px)"></path>
									<animateMotion dur="10s" begin="0s" repeatCount="indefinite" rotate="auto">
										<mpath xlink:href="#shopPath"></mpath>
									</animateMotion>
								</g>
								<g class="arrow a2">
									<path d="M 0 0 L 8 8 L0 16" fill="none" stroke="#fff" stroke-opacity="0.5"
										stroke-width="3" stroke-linecap="round" stroke-linejoin="round"
										style="transform:translateY(-8px)"></path>
									<animateMotion dur="10s" begin="0.33s" repeatCount="indefinite" rotate="auto">
										<mpath xlink:href="#shopPath"></mpath>
									</animateMotion>
								</g>
								<g class="arrow a3">
									<path d="M 0 0 L 8 8 L0 16" fill="none" stroke="#fff" stroke-opacity="0.5"
										stroke-width="3" stroke-linecap="round" stroke-linejoin="round"
										style="transform:translateY(-8px)"></path>
									<animateMotion dur="10s" begin="0.66s" repeatCount="indefinite" rotate="auto">
										<mpath xlink:href="#shopPath"></mpath>
									</animateMotion>
								</g>
								<g class="arrow a4">
									<path d="M 0 0 L 8 8 L0 16" fill="none" stroke="#fff" stroke-opacity="0.5"
										stroke-width="3" stroke-linecap="round" stroke-linejoin="round"
										style="transform:translateY(-8px)"></path>
									<animateMotion dur="10s" begin="0.99s" repeatCount="indefinite" rotate="auto">
										<mpath xlink:href="#shopPath"></mpath>
									</animateMotion>
								</g>
								<g class="arrow a5">
									<path d="M 0 0 L 8 8 L0 16" fill="none" stroke="#fff" stroke-opacity="0.5"
										stroke-width="3" stroke-linecap="round" stroke-linejoin="round"
										style="transform:translateY(-8px)"></path>
									<animateMotion dur="10s" begin="1.32s" repeatCount="indefinite" rotate="auto">
										<mpath xlink:href="#shopPath"></mpath>
									</animateMotion>
								</g>
								<g class="arrow a6">
									<path d="M 0 0 L 8 8 L0 16" fill="none" stroke="#fff" stroke-opacity="0.5"
										stroke-width="3" stroke-linecap="round" stroke-linejoin="round"
										style="transform:translateY(-8px)"></path>
									<animateMotion dur="10s" begin="1.6500000000000001s" repeatCount="indefinite"
										rotate="auto">
										<mpath xlink:href="#shopPath"></mpath>
									</animateMotion>
								</g>
								<g class="arrow a7">
									<path d="M 0 0 L 8 8 L0 16" fill="none" stroke="#fff" stroke-opacity="0.5"
										stroke-width="3" stroke-linecap="round" stroke-linejoin="round"
										style="transform:translateY(-8px)"></path>
									<animateMotion dur="10s" begin="1.98s" repeatCount="indefinite" rotate="auto">
										<mpath xlink:href="#shopPath"></mpath>
									</animateMotion>
								</g>
								<g class="arrow a8">
									<path d="M 0 0 L 8 8 L0 16" fill="none" stroke="#fff" stroke-opacity="0.5"
										stroke-width="3" stroke-linecap="round" stroke-linejoin="round"
										style="transform:translateY(-8px)"></path>
									<animateMotion dur="10s" begin="2.31s" repeatCount="indefinite" rotate="auto">
										<mpath xlink:href="#shopPath"></mpath>
									</animateMotion>
								</g>
								<g class="arrow a9">
									<path d="M 0 0 L 8 8 L0 16" fill="none" stroke="#fff" stroke-opacity="0.5"
										stroke-width="3" stroke-linecap="round" stroke-linejoin="round"
										style="transform:translateY(-8px)"></path>
									<animateMotion dur="10s" begin="2.64s" repeatCount="indefinite" rotate="auto">
										<mpath xlink:href="#shopPath"></mpath>
									</animateMotion>
								</g>
								<g class="arrow a10">
									<path d="M 0 0 L 8 8 L0 16" fill="none" stroke="#fff" stroke-opacity="0.5"
										stroke-width="3" stroke-linecap="round" stroke-linejoin="round"
										style="transform:translateY(-8px)"></path>
									<animateMotion dur="10s" begin="2.97s" repeatCount="indefinite" rotate="auto">
										<mpath xlink:href="#shopPath"></mpath>
									</animateMotion>
								</g>
								<g class="arrow a11">
									<path d="M 0 0 L 8 8 L0 16" fill="none" stroke="#fff" stroke-opacity="0.5"
										stroke-width="3" stroke-linecap="round" stroke-linejoin="round"
										style="transform:translateY(-8px)"></path>
									<animateMotion dur="10s" begin="3.3000000000000003s" repeatCount="indefinite"
										rotate="auto">
										<mpath xlink:href="#shopPath"></mpath>
									</animateMotion>
								</g>
								<g class="arrow a12">
									<path d="M 0 0 L 8 8 L0 16" fill="none" stroke="#fff" stroke-opacity="0.5"
										stroke-width="3" stroke-linecap="round" stroke-linejoin="round"
										style="transform:translateY(-8px)"></path>
									<animateMotion dur="10s" begin="3.6300000000000003s" repeatCount="indefinite"
										rotate="auto">
										<mpath xlink:href="#shopPath"></mpath>
									</animateMotion>
								</g>
								<g class="arrow a13">
									<path d="M 0 0 L 8 8 L0 16" fill="none" stroke="#fff" stroke-opacity="0.5"
										stroke-width="3" stroke-linecap="round" stroke-linejoin="round"
										style="transform:translateY(-8px)"></path>
									<animateMotion dur="10s" begin="3.96s" repeatCount="indefinite" rotate="auto">
										<mpath xlink:href="#shopPath"></mpath>
									</animateMotion>
								</g>
								<g class="arrow a14">
									<path d="M 0 0 L 8 8 L0 16" fill="none" stroke="#fff" stroke-opacity="0.5"
										stroke-width="3" stroke-linecap="round" stroke-linejoin="round"
										style="transform:translateY(-8px)"></path>
									<animateMotion dur="10s" begin="4.29s" repeatCount="indefinite" rotate="auto">
										<mpath xlink:href="#shopPath"></mpath>
									</animateMotion>
								</g>
								<g class="arrow a15">
									<path d="M 0 0 L 8 8 L0 16" fill="none" stroke="#fff" stroke-opacity="0.5"
										stroke-width="3" stroke-linecap="round" stroke-linejoin="round"
										style="transform:translateY(-8px)"></path>
									<animateMotion dur="10s" begin="4.62s" repeatCount="indefinite" rotate="auto">
										<mpath xlink:href="#shopPath"></mpath>
									</animateMotion>
								</g>
								<g class="arrow a16">
									<path d="M 0 0 L 8 8 L0 16" fill="none" stroke="#fff" stroke-opacity="0.5"
										stroke-width="3" stroke-linecap="round" stroke-linejoin="round"
										style="transform:translateY(-8px)"></path>
									<animateMotion dur="10s" begin="4.95s" repeatCount="indefinite" rotate="auto">
										<mpath xlink:href="#shopPath"></mpath>
									</animateMotion>
								</g>
								<g class="arrow a17">
									<path d="M 0 0 L 8 8 L0 16" fill="none" stroke="#fff" stroke-opacity="0.5"
										stroke-width="3" stroke-linecap="round" stroke-linejoin="round"
										style="transform:translateY(-8px)"></path>
									<animateMotion dur="10s" begin="5.28s" repeatCount="indefinite" rotate="auto">
										<mpath xlink:href="#shopPath"></mpath>
									</animateMotion>
								</g>
								<g class="arrow a18">
									<path d="M 0 0 L 8 8 L0 16" fill="none" stroke="#fff" stroke-opacity="0.5"
										stroke-width="3" stroke-linecap="round" stroke-linejoin="round"
										style="transform:translateY(-8px)"></path>
									<animateMotion dur="10s" begin="5.61s" repeatCount="indefinite" rotate="auto">
										<mpath xlink:href="#shopPath"></mpath>
									</animateMotion>
								</g>
								<g class="arrow a19">
									<path d="M 0 0 L 8 8 L0 16" fill="none" stroke="#fff" stroke-opacity="0.5"
										stroke-width="3" stroke-linecap="round" stroke-linejoin="round"
										style="transform:translateY(-8px)"></path>
									<animateMotion dur="10s" begin="5.94s" repeatCount="indefinite" rotate="auto">
										<mpath xlink:href="#shopPath"></mpath>
									</animateMotion>
								</g>
								<g class="arrow a20">
									<path d="M 0 0 L 8 8 L0 16" fill="none" stroke="#fff" stroke-opacity="0.5"
										stroke-width="3" stroke-linecap="round" stroke-linejoin="round"
										style="transform:translateY(-8px)"></path>
									<animateMotion dur="10s" begin="6.2700000000000005s" repeatCount="indefinite"
										rotate="auto">
										<mpath xlink:href="#shopPath"></mpath>
									</animateMotion>
								</g>
								<g class="arrow a21">
									<path d="M 0 0 L 8 8 L0 16" fill="none" stroke="#fff" stroke-opacity="0.5"
										stroke-width="3" stroke-linecap="round" stroke-linejoin="round"
										style="transform:translateY(-8px)"></path>
									<animateMotion dur="10s" begin="6.6000000000000005s" repeatCount="indefinite"
										rotate="auto">
										<mpath xlink:href="#shopPath"></mpath>
									</animateMotion>
								</g>
								<g class="arrow a22">
									<path d="M 0 0 L 8 8 L0 16" fill="none" stroke="#fff" stroke-opacity="0.5"
										stroke-width="3" stroke-linecap="round" stroke-linejoin="round"
										style="transform:translateY(-8px)"></path>
									<animateMotion dur="10s" begin="6.930000000000001s" repeatCount="indefinite"
										rotate="auto">
										<mpath xlink:href="#shopPath"></mpath>
									</animateMotion>
								</g>
								<g class="arrow a23">
									<path d="M 0 0 L 8 8 L0 16" fill="none" stroke="#fff" stroke-opacity="0.5"
										stroke-width="3" stroke-linecap="round" stroke-linejoin="round"
										style="transform:translateY(-8px)"></path>
									<animateMotion dur="10s" begin="7.260000000000001s" repeatCount="indefinite"
										rotate="auto">
										<mpath xlink:href="#shopPath"></mpath>
									</animateMotion>
								</g>
								<g class="arrow a24">
									<path d="M 0 0 L 8 8 L0 16" fill="none" stroke="#fff" stroke-opacity="0.5"
										stroke-width="3" stroke-linecap="round" stroke-linejoin="round"
										style="transform:translateY(-8px)"></path>
									<animateMotion dur="10s" begin="7.590000000000001s" repeatCount="indefinite"
										rotate="auto">
										<mpath xlink:href="#shopPath"></mpath>
									</animateMotion>
								</g>
								<g class="arrow a25">
									<path d="M 0 0 L 8 8 L0 16" fill="none" stroke="#fff" stroke-opacity="0.5"
										stroke-width="3" stroke-linecap="round" stroke-linejoin="round"
										style="transform:translateY(-8px)"></path>
									<animateMotion dur="10s" begin="7.92s" repeatCount="indefinite" rotate="auto">
										<mpath xlink:href="#shopPath"></mpath>
									</animateMotion>
								</g>
								<g class="arrow a26">
									<path d="M 0 0 L 8 8 L0 16" fill="none" stroke="#fff" stroke-opacity="0.5"
										stroke-width="3" stroke-linecap="round" stroke-linejoin="round"
										style="transform:translateY(-8px)"></path>
									<animateMotion dur="10s" begin="8.25s" repeatCount="indefinite" rotate="auto">
										<mpath xlink:href="#shopPath"></mpath>
									</animateMotion>
								</g>
								<g class="arrow a27">
									<path d="M 0 0 L 8 8 L0 16" fill="none" stroke="#fff" stroke-opacity="0.5"
										stroke-width="3" stroke-linecap="round" stroke-linejoin="round"
										style="transform:translateY(-8px)"></path>
									<animateMotion dur="10s" begin="8.58s" repeatCount="indefinite" rotate="auto">
										<mpath xlink:href="#shopPath"></mpath>
									</animateMotion>
								</g>
								<g class="arrow a28">
									<path d="M 0 0 L 8 8 L0 16" fill="none" stroke="#fff" stroke-opacity="0.5"
										stroke-width="3" stroke-linecap="round" stroke-linejoin="round"
										style="transform:translateY(-8px)"></path>
									<animateMotion dur="10s" begin="8.91s" repeatCount="indefinite" rotate="auto">
										<mpath xlink:href="#shopPath"></mpath>
									</animateMotion>
								</g>
								<g class="arrow a29">
									<path d="M 0 0 L 8 8 L0 16" fill="none" stroke="#fff" stroke-opacity="0.5"
										stroke-width="3" stroke-linecap="round" stroke-linejoin="round"
										style="transform:translateY(-8px)"></path>
									<animateMotion dur="10s" begin="9.24s" repeatCount="indefinite" rotate="auto">
										<mpath xlink:href="#shopPath"></mpath>
									</animateMotion>
								</g>
								<g class="arrow a30">
									<path d="M 0 0 L 8 8 L0 16" fill="none" stroke="#fff" stroke-opacity="0.5"
										stroke-width="3" stroke-linecap="round" stroke-linejoin="round"
										style="transform:translateY(-8px)"></path>
									<animateMotion dur="10s" begin="9.57s" repeatCount="indefinite" rotate="auto">
										<mpath xlink:href="#shopPath"></mpath>
									</animateMotion>
								</g>
							</g>
						</svg>
					</view>
					<!-- 购物流程节点 start -->
					<ul class="pshop-nodes" id="mstValueIcos" style="list-style-type: none;display: none;">
						<li class="item item1">
							<view class="box">
								<view class="txtbox">
									<view class="tit">
										Discover Short Films/High-Quality Video
									</view>
								</view>
							</view>
						</li>
						<li class="item item2">
							<view class="box">
								<view class="txtbox">
									<view class="tit">
										Discover Short Films/High-Quality Video
									</view>
								</view>
							</view>
						</li>
						<li class="item item3">
							<view class="box">
								<view class="txtbox">
									<view class="tit">
										Discover Short Films/High-Quality Video
									</view>
								</view>
							</view>
						</li>
						<li class="item item4">
							<view class="box">
								<view class="txtbox">
									<view class="tit">
										Discover Short Films/High-Quality Video
									</view>
								</view>
							</view>
						</li>
					</ul>

				</view>
				<view v-if="ispc === false" class="app_dri_boxbody">
					<view class="app_dri_box">
						<view class="dritit">
							{{$t('full[0].tit')}}
						</view>
						<view class="drimes">
							{{$t('full[0].mes')}}
						</view>
					</view>
					<view class="app_dri_box">
						<view class="dritit">
							{{$t('full[1].tit')}}
						</view>
						<view class="drimes">
							{{$t('full[1].mes')}}
						</view>
					</view>
					<view class="app_dri_box">
						<view class="dritit">
							{{$t('full[2].tit')}}
						</view>
						<view class="drimes">
							{{$t('full[2].mes')}}
						</view>
					</view>
					<view class="app_dri_box">
						<view class="dritit">
							{{$t('full[3].tit')}}
						</view>
						<view class="drimes">
							{{$t('full[3].mes')}}
						</view>
					</view>
				</view>
			</view>
		</view>
		<view class="platform_boxbody" style="overflow: hidden;">
			<view class="pla_top_mesbox">
				<view v-if="ispc" class="" style="color: #BAFC51;">
					<!-- Creators Flooding the Platform -->
					{{$t('platform.tit1')}}
				</view>
				<view v-else class="" style="color: #BAFC51;display: inline;">
					{{$t('platform.tit1')}}
				</view>
				{{$t('platform.tit2')}}
			</view>
			<view class="pla_img_box" v-if="ispc===true">
				<!-- <image src="../../static/image/Frame.png" mode=""></image> -->
				<!-- web端瀑布流 start -->
				<view class="mstValue1-imgsbox hidden-lg-down" style=" height: 1000px;"
					@mousewheel.stop="handleMouseWheel2">
					<view class="mstValue1-imgs">
						<view class="row">
							<!-- imgslist start -->
							<view class="imgslist list1" id="valueImgList1"
								:style="{'transform':`translateY(${trans1}%)`}">
								<view class="item">
									<ul class="item-list">
										<li>
											<view class="box">
												<image
													src="https://www.tiktokforbusinessoutbound.com/img/market/value/1.png"
													mode="widthFix"></image>
											</view>
										</li>
										<li>
											<view class="box">
												<image
													src="https://www.tiktokforbusinessoutbound.com/img/market/value/2.png"
													mode="widthFix"></image>
											</view>
										</li>
									</ul>
									<view class="item-tit">
										#TipsBelanja
									</view>
								</view>
								<view class="item">
									<ul class="item-list">
										<li>
											<view class="box">
												<image
													src="https://www.tiktokforbusinessoutbound.com/img/market/value/3.png"
													mode="widthFix"></image>
											</view>
										</li>
									</ul>
								</view>
							</view>
							<view class="imgslist list1" id="valueImgList1"
								:style="{'transform':`translateY(${trans2}%)`}">
								<view class="item">
									<ul class="item-list">
										<li>
											<view class="box">
												<image
													src="https://www.tiktokforbusinessoutbound.com/img/market/value/4.png"
													mode="widthFix"></image>
											</view>
										</li>
										<li>
											<view class="box">
												<image
													src="https://www.tiktokforbusinessoutbound.com/img/market/value/5.png"
													mode="widthFix"></image>
											</view>
										</li>
									</ul>
									<view class="item-tit">
										#BagikanDiskon
									</view>
								</view>
								<view class="item">
									<ul class="item-list">
										<li>
											<view class="box">
												<image
													src="https://www.tiktokforbusinessoutbound.com/img/market/value/6.png"
													mode="widthFix"></image>
											</view>
										</li>
									</ul>
								</view>
							</view>
							<view class="imgslist list1" id="valueImgList1"
								:style="{'transform':`translateY(${trans3}%)`}">
								<view class="item">
									<ul class="item-list">
										<li>
											<view class="box">
												<image
													src="https://www.tiktokforbusinessoutbound.com/img/market/value/7.png"
													mode="widthFix"></image>
											</view>
										</li>
										<li>
											<view class="box">
												<image
													src="https://www.tiktokforbusinessoutbound.com/img/market/value/8.png"
													mode="widthFix"></image>
											</view>
										</li>
									</ul>
									<view class="item-tit">
										#UnboxingOOTD
									</view>
								</view>
								<view class="item">
									<ul class="item-list">
										<li>
											<view class="box">
												<image
													src="https://www.tiktokforbusinessoutbound.com/img/market/value/9.png"
													mode="widthFix"></image>
											</view>
										</li>
									</ul>
								</view>
							</view>
							<view class="imgslist list1" id="valueImgList1"
								:style="{'transform':`translateY(${trans4}%)`}">
								<view class="item">
									<ul class="item-list">
										<li>
											<view class="box">
												<image
													src="https://www.tiktokforbusinessoutbound.com/img/market/value/10.png"
													mode="widthFix"></image>
											</view>
										</li>
										<li>
											<view class="box">
												<image
													src="https://www.tiktokforbusinessoutbound.com/img/market/value/11.png"
													mode="widthFix"></image>
											</view>
										</li>
									</ul>
									<view class="item-tit">
										#RekomendasiProduk
									</view>
								</view>
								<view class="item">
									<ul class="item-list">
										<li>
											<view class="box">
												<image
													src="https://www.tiktokforbusinessoutbound.com/img/market/value/12.png"
													mode="widthFix"></image>
											</view>
										</li>
									</ul>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
			<view v-else class="app_img_box">
				<view class="leftimgbox">
					<image src="../../static/image/List (4).png" mode=""></image>
				</view>
				<view class="rightimgbox">
					<image src="../../static/image/List (5).png" mode=""></image>
				</view>
			</view>
		</view>
		<view class="partners_boxbody">
			<view class="partners_box">
				<image v-if="ispc === true" src="../../static/image/div.investor.png" mode=""></image>
				<image v-else src="../../static/image/div.investor1.png" mode="widthFix"></image>
			</view>
		</view>
		<view class="team_boxbody">
			<view class="team_box_body">
				<view class="t_top_tit">
					<view class="" style="color: #BAFC51;display: inline;">
						{{$t('team.tit1')}}
					</view> {{$t('team.tit2')}}
				</view>
				<view class="t_top_mes">
					{{$t('team.tit3')}}
				</view>
				<view v-if="ispc===true" class="" style="width: 100%;height: auto;">
					<view class="t_bot_tabbox_body">
						<view class="t_tab_box">
							<view class="tab_top_userbox">
								<view class="userimg">
									<image src="../../static/image/team/img4.png.png" mode=""></image>
								</view>
								<view class="usermes">
									<view class="name">
										{{$t('team.teamtab[0].name')}}
									</view>
									<view class="zw">
										{{$t('team.teamtab[0].job')}}
									</view>
								</view>
							</view>
							<view class="tab_mes_box">
								{{$t('team.teamtab[0].mes1')}}
							</view>
							<view class="tab_keywords">
								{{$t('team.teamtab[0].mes2')}}
							</view>
							<view class="tab_follower">
								<view class="followermes">
									Followers: 762K
								</view>
								<view class="followerimg">
									<image src="../../static/image/team/SVG (1).png" mode=""></image>
								</view>
							</view>
						</view>
						<view class="t_tab_box">
							<view class="tab_top_userbox">
								<view class="userimg">
									<image src="../../static/image/team/img4.png (1).png" mode=""></image>
								</view>
								<view class="usermes">
									<view class="name">
										{{$t('team.teamtab[1].name')}}
									</view>
									<view class="zw">
										{{$t('team.teamtab[1].job')}}
									</view>
								</view>
							</view>
							<view class="tab_mes_box">
								{{$t('team.teamtab[1].mes1')}}
							</view>
							<view class="tab_keywords">
								{{$t('team.teamtab[1].mes2')}}
							</view>
							<view class="tab_follower">
								<view class="followermes">
									Followers: 55k
								</view>
								<view class="followerimg">
									<image src="../../static/image/team/SVG (1).png" mode=""></image>
								</view>
							</view>
						</view>
						<view class="t_tab_box">
							<view class="tab_top_userbox">
								<view class="userimg">
									<image src="../../static/image/team/img4.png (2).png" mode=""></image>
								</view>
								<view class="usermes">
									<view class="name">
										{{$t('team.teamtab[2].name')}}
									</view>
									<view class="zw">
										{{$t('team.teamtab[2].job')}}
									</view>
								</view>
							</view>
							<view class="tab_mes_box">
								{{$t('team.teamtab[2].mes1')}}
							</view>
							<view class="tab_keywords">
								{{$t('team.teamtab[2].mes2')}}
							</view>
							<view class="tab_follower">
								<view class="followermes">
									Followers: 85k
								</view>
								<view class="followerimg">
									<image src="../../static/image/team/SVG (1).png" mode=""></image>
								</view>
							</view>
						</view>
						<view class="t_tab_box">
							<view class="tab_top_userbox">
								<view class="userimg">
									<image src="../../static/image/team/img4.png (3).png" mode=""></image>
								</view>
								<view class="usermes">
									<view class="name">
										{{$t('team.teamtab[3].name')}}
									</view>
									<view class="zw">
										{{$t('team.teamtab[3].job')}}
									</view>
								</view>
							</view>
							<view class="tab_mes_box">
								{{$t('team.teamtab[3].mes1')}}
							</view>
							<view class="tab_keywords">
								{{$t('team.teamtab[3].mes2')}}
							</view>
							<view class="tab_follower">
								<view class="followermes">
									Followers: 192K
								</view>
								<view class="followerimg">
									<image src="../../static/image/team/SVG (1).png" mode=""></image>
								</view>
							</view>
						</view>
					</view>
				</view>

				<view v-else class="t_bot_tabbox_body">
					<scroll-view class="botsscroll" scroll-x="true" style="white-space: nowrap;">
						<view class="t_tab_box">
							<view class="tab_top_userbox">
								<view class="userimg">
									<image src="../../static/image/team/img4.png.png" mode=""></image>
								</view>
								<view class="usermes">
									<view class="name">
										{{$t('team.teamtab[0].name')}}
									</view>
									<view class="zw">
										{{$t('team.teamtab[0].job')}}
									</view>
								</view>
							</view>
							<view class="tab_mes_box">
								{{$t('team.teamtab[0].mes1')}}
							</view>
							<view class="tab_keywords">
								{{$t('team.teamtab[0].mes2')}}
							</view>
							<view class="tab_follower">
								<view class="followermes">
									Followers: 762K
								</view>
								<view class="followerimg">
									<image src="../../static/image/team/SVG (1).png" mode=""></image>
								</view>
							</view>
						</view>
						<view class="t_tab_box">
							<view class="tab_top_userbox">
								<view class="userimg">
									<image src="../../static/image/team/img4.png (1).png" mode=""></image>
								</view>
								<view class="usermes">
									<view class="name">
										{{$t('team.teamtab[1].name')}}
									</view>
									<view class="zw">
										{{$t('team.teamtab[1].job')}}
									</view>
								</view>
							</view>
							<view class="tab_mes_box">
								{{$t('team.teamtab[1].mes1')}}
							</view>
							<view class="tab_keywords">
								{{$t('team.teamtab[1].mes2')}}
							</view>
							<view class="tab_follower">
								<view class="followermes">
									Followers: 55k
								</view>
								<view class="followerimg">
									<image src="../../static/image/team/SVG (1).png" mode=""></image>
								</view>
							</view>
						</view>
						<view class="t_tab_box">
							<view class="tab_top_userbox">
								<view class="userimg">
									<image src="../../static/image/team/img4.png (2).png" mode=""></image>
								</view>
								<view class="usermes">
									<view class="name">
										{{$t('team.teamtab[2].name')}}
									</view>
									<view class="zw">
										{{$t('team.teamtab[2].job')}}
									</view>
								</view>
							</view>
							<view class="tab_mes_box">
								{{$t('team.teamtab[2].mes1')}}
							</view>
							<view class="tab_keywords">
								{{$t('team.teamtab[2].mes2')}}
							</view>
							<view class="tab_follower">
								<view class="followermes">
									Followers: 85k
								</view>
								<view class="followerimg">
									<image src="../../static/image/team/SVG (1).png" mode=""></image>
								</view>
							</view>
						</view>
						<view class="t_tab_box">
							<view class="tab_top_userbox">
								<view class="userimg">
									<image src="../../static/image/team/img4.png (3).png" mode=""></image>
								</view>
								<view class="usermes">
									<view class="name">
										{{$t('team.teamtab[3].name')}}
									</view>
									<view class="zw">
										{{$t('team.teamtab[3].job')}}
									</view>
								</view>
							</view>
							<view class="tab_mes_box">
								{{$t('team.teamtab[3].mes1')}}
							</view>
							<view class="tab_keywords">
								{{$t('team.teamtab[3].mes2')}}
							</view>
							<view class="tab_follower">
								<view class="followermes">
									Followers: 192K
								</view>
								<view class="followerimg">
									<image src="../../static/image/team/SVG (1).png" mode=""></image>
								</view>
							</view>
						</view>
					</scroll-view>
				</view>
			</view>
		</view>
		<view class="bottom_box_body">
			<view v-if="ispc" class="top_concactus_boxbody">
				<view class="concactus_box_body">
					<view class="c_toptit">
						{{$t('Support')}}
					</view>
					<view class="c_topmes">
						{{$t('ecosystem')}}
					</view>
					<view class="c_an" @click="gotowburl">
						{{$t('ContactUs')}}
					</view>
				</view>
			</view>
			<view class="bot_concactus_boxbody">
				<!-- <image v-if="ispc === true" src="../../static/image/Footer.png" mode=""></image> -->
				<view v-if="ispc === true" class="bottom_joinbo">
					<!-- <image src="../../static/image/Footer.png" mode=""></image> -->
					<view class="dh_menu_boxbody">
						<view class="dhtit1">
							{{$t('dhmenu[0].tit')}}
						</view>
						<view class="dhtit2">
							{{$t('dhmenu[0].mes1')}}
						</view>
						<view class="dhtit2">
							{{$t('dhmenu[0].mes2')}}
						</view>
					</view>
					<view class="dh_menu_boxbody">
						<view class="dhtit1">
							{{$t('dhmenu[1].tit')}}
						</view>
						<view class="dhtit2">
							{{$t('dhmenu[1].mes1')}}
						</view>
						<view class="dhtit2">
							{{$t('dhmenu[1].mes2')}}
						</view>
					</view>
					<view class="dh_menu_boxbody">
						<view class="dhtit1">
							{{$t('FollowUs')}}
						</view>
						<view class="dhiconboxbody">
							<view class="dhicon" style="margin-right: 17px;" @click="gotowburl">
								<image src="../../static/image/join/Item.png" mode=""></image>
							</view>
							<view class="dhicon" @click="gotowburl">
								<image src="../../static/image/join/Item (1).png" mode=""></image>
							</view>
						</view>
					</view>
					<view class="Subscribe_boxbody">
						<view class="Subscribe_topbox">
							{{$t('dhmenu[2].tit')}}
							<view class="sub_box">
								{{$t('dhmenu[2].mes1')}}
							</view>
						</view>
						<view class="subtit">
							{{$t('dhmenu[2].mes2')}}
						</view>
					</view>
				</view>
				<view v-else class=""
					style="width: 100%;height: 100%;position: relative;display: flex;flex-direction: column;">
					<!-- 					<image src="../../static/image/Footer (2).png" mode=""></image>
					<view class=""
						style="display: flex;width: 100%;height: 2.5rem;flex-direction: row;align-items: center;color: rgba(255, 255, 255, 0.5);position: absolute;bottom: 0;justify-content: center;font-size: 20rpx;">
						<view class="" style="display: inline;margin-right: 30rpx;">
							Privacy Policy
						</view>Copyright © 2025 Funshot
					</view> -->
					<view class="app_bot_dhboxbody">
						<view class="logoboxbody">
							<view class="logobox">
								<image src="../../static/image/SVG1.png" mode=""></image>
							</view>
							<view class="logotit_box">
								<view class="toptit">
									{{$t('appbot.FunshotApp')}}
								</view>
								<view class="bottit">
									{{$t('appbot.Generation')}}
								</view>
							</view>
						</view>
						<view class="dhboxbody">
							<view class="ttit">
								{{$t('appbot.list[0].tit1')}}
							</view>
							<view class="ctit">
								{{$t('appbot.list[0].tit2')}}
							</view>
							<view class="ctit" style="margin-top: 0;">
								{{$t('appbot.list[0].tit3')}}
							</view>
						</view>
						<view class="dhboxbody">
							<view class="ttit">
								{{$t('appbot.list[1].tit1')}}
							</view>
							<view class="ctit">
								{{$t('appbot.list[1].tit2')}}
							</view>
							<view class="ctit" style="margin-top: 0;">
								{{$t('appbot.list[1].tit3')}}
							</view>
						</view>
						<view class="dhboxbody">
							<view class="ttit">
								{{$t('appbot.list[2].tit1')}}
							</view>
							<view class="ctit">
								{{$t('appbot.list[2].tit2')}}
							</view>
							<view class="ctit" style="margin-top: 0;">
								{{$t('appbot.list[2].tit3')}}
							</view>
						</view>
						<view class="" style="width: 100%;height: auto;">
							<image src="../../static/image/div.box (8).png" mode="widthFix"></image>
						</view>
					</view>
					<view class=""
						style="width: 100%;height: auto;padding: 40rpx 0;box-sizing: border-box;background-color: #000;font-size: 20rpx;color: rgba(255, 255, 255, .5);display: flex;align-items: center;justify-content: center;">
						<view class="" style="display: inline;margin-right: 30rpx;">
							{{$t('appbot.Privacy')}}
						</view>{{$t('appbot.Copyright')}}
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script setup>
	import {
		onLoad,
		onReady,
		onShow
	} from '@dcloudio/uni-app';
	import {
		ref,
		reactive,
		onMounted,
		onBeforeMount,
		watch
	} from 'vue';

	import {
		useI18n
	} from 'vue-i18n';
	const {
		t,
		locale
	} = useI18n();

	const toView = ref('')
	const sctop = ref(0)

	const title = ref('hello')
	const menulist = ref(['Overview', 'Performance', 'Features', 'Advantages', 'Join', 'Download'])
	const menuactive = ref(-1)
	const langlist = ref(['Indonesian', 'Eenglish', 'Chinese'])
	const langch = ref(false)

	const viimg = ref(['../../static/image/vision/3.png', '../../static/image/vision/4.png',
		'../../static/image/vision/5.png'
	])

	const fullxz = ref([{
			toptit: 'Full-chain short video growth support',
			xzmes: 'Creative Ideation',
			url: '../../static/image/full/Frame 128.png',
			tit1: 'Find your next growth breakthrough',
			tit2: 'Funshot Growth Support Center',
			mes: 'invitation systems, task incentives, and creative content. Offers tools and data interfaces to help brands achieve rapid expansion and community engagement.'
		},
		{
			toptit: 'Full-chain creative support',
			xzmes: 'Content Production',
			url: '../../static/image/full/Frame 130.png',
			tit1: 'Find your next popular video',
			tit2: 'Funshot Content Creation Center',
			mes: 'Discover your next hit video with a vast array of official templates, popular subtitle styles, and sound effects. Our platform integrates content incentive mechanisms and tagging systems to help creators efficiently produce engaging video content.'
		},
		{
			toptit: 'Full-chain creative support',
			xzmes: 'Invitation & Growth',
			url: '../../static/image/full/Frame 131.png',
			tit1: 'The lever for growth lies within relationship networks.',
			tit2: 'Funshot Growth Toolbox',
			mes: 'Leverage relationship networks to drive growth. Through the F-Bean incentive system, team dynamics, and unique invitation codes, users are motivated to recruit, share, and form teams, creating a sustainable growth cycle.'
		},
		{
			toptit: 'Full-chain creative support',
			xzmes: 'Data Growth',
			url: '../../static/image/full/Frame 129.png',
			tit1: 'Growth isn' + 't mystical; it' + 's a quantifiable long-term strategy.',
			tit2: 'Funshot Data Insight System',
			mes: 'Growth isn' + 't mystical; it' +
				's quantifiable. The system features dashboards for real-time tracking of key metrics like new users, activity, and F-Bean production. It combines individual and team perspectives to help users and creators optimize their growth strategies.'
		}
	])
	const fullxzinx = ref(0)

	const learnchange = ref(['Smart Fix', 'Smart Creative'])
	const lchaninx = ref(0)

	const tb = ref(['TopView', 'BM'])
	const tbinx = ref(0)
	const flfdata = ref([{
			tit: 'Driven by Revenue Sharing',
			mes: 'Encouraging participation and growth, Funshot uses "watch videos to earn F-Beans and invite friends for rewards" as its core mechanism, creating a user-centric short video platform ecosystem.',
			num: '1587',
			dw: 'W+'
		},
		{
			tit: 'User Co-Creation',
			mes: 'Earn while watching, share for rewards. Incentives cover viewing, creating, and inviting actions, achieving mutual value for the platform and users.',
			num: '8587',
			dw: 'W+'
		},
		{
			tit: 'Continuous income',
			mes: 'Earnings continuously accumulate with permanent invitation binding, ensuring long-term, non-diminishing income.',
			num: '28.6',
			dw: 'Hour/day'
		}
	])
	const flfinx = ref(0)

	const alitab = ref([{
			url: '../../static/image/ali/1.png',
			svg: '../../static/image/svg/1.svg'
		},
		{
			url: '../../static/image/ali/2.png',
			svg: '../../static/image/svg/2.svg'
		},
		{
			url: '../../static/image/ali/3.png',
			svg: '../../static/image/svg/3.svg'
		}
	])
	const aliinx = ref(0)

	const currentinx = ref(0)

	const leftchose = ref([{
			imgurl: '../../static/image/Background+Border.png'
		},
		{
			imgurl: '../../static/image/Background+Border1.png'
		},
		{
			imgurl: '../../static/image/Background+Border2.png'
		},
		{
			imgurl: '../../static/image/Background+Border3.png'
		}
	])
	const ispc = ref(null)
	const trans1 = ref(37.7)
	const trans2 = ref(15.5)
	const trans3 = ref(12.7)
	const trans4 = ref(19.4)
	const traninx = ref(0)
	const lan = ref('lang.Eenglish')
	const windowWidth = ref(window.innerWidth);
	const canScroll = ref(true)
	const lastinx = ref(0)
	onBeforeMount(() => {
		if (uni.getStorageSync('lan')) {
			lan.value = 'lang.' + uni.getStorageSync('lan')
		}

		console.log(uni.getStorageSync('ispc'));
		ispc.value = uni.getStorageSync('ispc')
		// playVideo()
	})
	onMounted(() => {
		window.addEventListener('resize', updateWindowWidth);
	})
	onShow(() => {
		checkAndSetBodyClass()
		console.log(uni.getStorageSync('menuainx'));
		// if (uni.getStorageSync('menuainx')) {
		// 	console.log('333333');
		// 	let inx = uni.getStorageSync('menuainx')
		// 	scrollTo111(menulist.value[inx])
		// }


	})
	onReady(() => {
		let inx = uni.getStorageSync('menuainx')
		if (inx !== '') {
			menuactive.value = parseInt(inx)
			scrollTo111(menulist.value[inx])
		}
	})
	watch(windowWidth, (newVal) => {
		console.log('新的窗口宽度:', newVal);
		checkAndSetBodyClass()
		// 在这里处理宽度变化逻辑
	});
	const leftchoseinx = ref(0)

	const updateWindowWidth = () => {
		windowWidth.value = window.innerWidth;
	};

	const checkAndSetBodyClass = () => {
		const width = window.innerWidth;
		const body = document.body;

		// 移除已有的类，然后根据需要添加新的类
		body.className = ''; // 清空所有class
		if (width < 960) {
			body.classList.add('mobile');
			uni.setStorageSync('ispc', false)
			ispc.value = false
		} else {
			body.classList.add('pc');
			uni.setStorageSync('ispc', true)
			ispc.value = true
		}
	}

	const currentchange = (e) => {
		console.log('currentchange', e);
		leftchoseinx.value = e.detail.current
	}

	const showlangchange = () => {
		langch.value = !langch.value
	}
	const playVideo = () => {
		const video = document.getElementById("playVideo")
		video.play().catch(error => {
			console.error('播放失败:', error);
		});
	}

	const gotowburl = () => {
		window.location.href = 'https://linktr.ee/funshot_io';
	}

	const lanchange = (item) => {
		console.log('lan', item);
		uni.setStorageSync('lan', item)
		if (item === 'Chinese') {
			uni.setStorageSync('lang', 'zh')
			location.reload();
		} else if (item === 'Eenglish') {
			uni.setStorageSync('lang', 'en')
			location.reload();
		} else {
			uni.setStorageSync('lang', 'id')
			location.reload();
		}

	}

	const scrollTo11 = (e) => {
		console.log(e.detail.scrollTop);
		if (e.detail.scrollTop > 1350) {
			leftchoseinx.value = 0
		}
		// if (e.detail.scrollTop > 50) {
		// 	isnavbar.value = true
		// } else {
		// 	isnavbar.value = false
		// }
	}

	const scrollTo = (id) => {
		console.log('id', id)
		toView.value = id
	}

	const menua = (index) => {
		menuactive.value = index
		if (index === 5) {
			uni.navigateTo({
				url: '/pages/download/index'
			})
		} else if (index === 4) {
			uni.navigateTo({
				url: '/pages/join/index'
			})
		} else if (index === -1) {
			scrollTo111('banner')
		} else {
			// uni.navigateTo({
			// 	url: '/pages/index/index'
			// })
			// toView.value = menulist.value[index]
			scrollTo111(menulist.value[index])
		}
	}
	const menum = (index) => {
		menuactive.value = index
		// lastinx.value = index
	}
	const menul = () => {
		console.log('移出');
		// setTimeout(function() {
		// 	if (lastinx.value === menuactive.value) {
		menuactive.value = 0
		// 	}
		// }, 500)
	}
	const scrollTo111 = (menulist) => {
		const scrollContainer = uni.createSelectorQuery().select('.scroll-container');
		scrollContainer.boundingClientRect(rect => {

			// 获取目标位置的元素（这里以菜单2为例）
			const target = uni.createSelectorQuery().select(`#${menulist}`);
			console.log('target', target);
			target.boundingClientRect(targetRect => {
				// 计算目标位置相对于滚动容器的偏移量
				console.log('targetRect', targetRect);
				const scrollTop = targetRect.top - rect.top;
				// 设置滚动容器的scrollTop属性，实现滚动效果
				console.log('scrollTop', scrollTop);
				uni.pageScrollTo({
					scrollTop: scrollTop,
					duration: 700 // 持续时间，单位ms
				});
				uni.removeStorageSync('menuainx')
			}).exec();
		}).exec();
	}
	const gotopage = () => {
		uni.reLaunch({
			url: '/pages/download/index'
		})
	}
	const leftcho = (index) => {
		leftchoseinx.value = index
	}
	const handleMouseWheel = (event) => {
		console.log('event', event);
		if (canScroll.value) {
			canScroll.value = false
			// event.preventDefault(); // 阻止默认行为，即阻止页面滚动
			if (leftchoseinx.value < 3) {
				leftchoseinx.value = leftchoseinx.value + 1
				event.preventDefault(); // 阻止默认行为，即阻止页面滚动
			} else {
				console.log('event', event);
				// leftchoseinx.value = 0
				// event.preventDefault(); // 阻止默认行为，即阻止页面滚动
			}
			setTimeout(() => {
				canScroll.value = true;
			}, 300);
		}
	}
	const handleMouseWheel2 = (event) => {
		if (canScroll.value) {
			canScroll.value = false
			event.preventDefault(); // 阻止默认行为，即阻止页面滚动
			if (traninx.value < 4) {
				trans1.value = trans1.value - 50
				trans2.value = trans2.value - 50
				trans3.value = trans3.value - 50
				trans4.value = trans4.value - 50
				traninx.value = traninx.value + 1
				console.log(traninx.value);
			} else {
				traninx.value = 0
				trans1.value = 37.7
				trans2.value = 15.5
				trans3.value = 12.7
				trans4.value = 19.4
			}
			setTimeout(() => {
				canScroll.value = true;
			}, 300);
		}


	}
	const tbclick = (index) => {
		tbinx.value = index
	}
	const flfclick = (index) => {
		flfinx.value = index
	}
	const handleMouseWheel1 = (event) => {
		// event.preventDefault(); // 阻止默认行为，即阻止页面滚动
		// if (flfinx.value < 2) {
		// 	flfinx.value = flfinx.value + 1
		// } else {
		// 	flfinx.value = 0
		// }

		if (canScroll.value) {
			canScroll.value = false
			if (flfinx.value < 2) {
				flfinx.value = flfinx.value + 1
				event.preventDefault(); // 阻止默认行为，即阻止页面滚动
			} else {
				// flfinx.value = 0
				console.log('event', event);
			}
			setTimeout(() => {
				canScroll.value = true;
			}, 300);
		}
	}

	const handleMouseWheel3 = (event) => {
		// event.preventDefault(); // 阻止默认行为，即阻止页面滚动
		// if (flfinx.value < 2) {
		// 	flfinx.value = flfinx.value + 1
		// } else {
		// 	flfinx.value = 0
		// }

		if (canScroll.value) {
			canScroll.value = false
			if (aliinx.value < 2) {
				aliinx.value = aliinx.value + 1
				event.preventDefault(); // 阻止默认行为，即阻止页面滚动
			} else {
				// flfinx.value = 0
				console.log('event', event);
			}
			setTimeout(() => {
				canScroll.value = true;
			}, 300);
		}
	}
	const aliclick = (index) => {
		aliinx.value = index
	}
	const fullxzclick = (index) => {
		fullxzinx.value = index
	}
	const lanclick = (index) => {
		lchaninx.value = index
	}

	const swiperchang = (e) => {
		// console.log('e', e.detail.current);
		currentinx.value = e.detail.current
	}
</script>

<style>
	.vi_rswiperboxbody {
		display: flex;
		width: 100%;
		/* 或者具体宽度 */
	}

	.viswpier {
		flex: 1;
		/* 让swiper填充父容器的高度 */
		width: 100%;
		/* 确保宽度为100% */
	}

	.vi_rswiperbox {
		display: flex;
		justify-content: center;
		align-items: center;
		width: 100%;
		/* 确保内容宽度为100% */
		height: auto;
		/* 高度自适应 */
	}
</style>

<style lang="scss">
	@import '../../static/scss/index.scss';

	video {
		width: 100%;
		height: 100%;
	}

	.spswiper ::v-deep .uni-swiper-dots-horizontal {
		left: 10% !important;
		bottom: 30px !important;
	}

	.spswiper ::v-deep .uni-swiper-dot {
		width: 30rpx !important;
		height: 8rpx !important;
		border-radius: 6rpx !important;
	}

	.spswiper ::v-deep .uni-swiper-dot-active {
		width: 100rpx !important;
		background-color: #BAFC51;
	}

	.spswiper1 ::v-deep .uni-swiper-dot-active {
		// width: 100rpx !important;
		background-color: #BAFC51;
	}

	.botsscroll ::v-deep .uni-scroll-view-content {
		display: -webkit-inline-box !important;
		// flex-direction: row !important;
	}

	.right_bo_mes_boxbody ::v-deep .swiper-initialized {
		height: 100% !important;
	}

	.right_bo_mes_boxbody ::v-deep .swiper {
		// display: flex !important;
		// flex-direction: row !important;
		height: 100% !important;
		width: 100% !important;
	}

	.right_bo_mes_boxbody ::v-deep .swiper uni-swiper-item {
		display: flex !important;
		flex-direction: row !important;
	}

	.right_bo_mes_boxbody ::v-deep .swiper uni-swiper-item .spitem {
		display: flex !important;
		flex-direction: row !important;
		transition: 0.5s;
		transform: scale(0, 0);
	}

	.right_bo_mes_boxbody ::v-deep .swiper uni-swiper-item .spitem.active {
		transform: scale(1, 1);
	}

	.ccswiper ::v-deep .swiper {
		// display: flex !important;
		// flex-direction: row !important;
		height: 100% !important;
		// width: 100% !important;
	}

	.aas ::v-deep .swiper {
		height: 1200rpx !important;
	}

	.aliexpress_boxbody ::v-deep .swiper {
		height: 200px !important;
	}

	.ccswiper ::v-deep .swiper uni-swiper-item {
		display: flex !important;
		// flex-direction: row !important;
	}
</style>