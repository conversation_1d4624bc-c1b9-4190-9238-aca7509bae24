<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FunShot - Kebahagiaan yang tidak diketahui orang lain</title>
    <meta name="description" content="Unduh FunShot, platform drama pendek dengan Neymar sebagai duta global. Tonton & hasilkan F-Bean sekarang!">
    
    <!-- Facebook Meta Tags -->
    <meta property="og:url" content="https://f-s.fun/">
    <meta property="og:type" content="website">
    <meta property="og:title" content="FunShot - Kebahagiaan yang tidak diketahui orang lain">
    <meta property="og:description" content="Unduh FunShot, platform drama pendek dengan Neymar sebagai duta global. Tonton & hasilkan F-Bean sekarang!">
    
    <link rel="icon" href="./static/image/logo1x.png" />
    <script src="https://res.openinstall.com/openinstall.js"></script>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #000000;
            color: white;
            line-height: 1.6;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
        
        .header {
            background-color: #000;
            padding: 20px 0;
            border-bottom: 1px solid #262626;
        }
        
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .logo-section {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .logo {
            width: 50px;
            height: 50px;
            border-radius: 10px;
            overflow: hidden;
        }
        
        .logo img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .app-info h1 {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .app-info p {
            font-size: 14px;
            color: #8E8E92;
        }
        
        .contact-btn {
            background-color: #BAFC51;
            color: #000;
            padding: 12px 24px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        
        .contact-btn:hover {
            background-color: #a8e045;
        }
        
        .hero-section {
            text-align: center;
            padding: 80px 0;
            background: linear-gradient(135deg, #000 0%, #1a1a1a 100%);
        }
        
        .hero-title {
            font-size: 4rem;
            font-weight: bold;
            margin-bottom: 20px;
        }
        
        .hero-title .highlight {
            color: #BAFC51;
        }
        
        .hero-subtitle {
            font-size: 1.5rem;
            color: rgba(255, 255, 255, 0.8);
            margin-bottom: 50px;
            max-width: 800px;
            margin-left: auto;
            margin-right: auto;
        }
        
        .download-section {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-bottom: 50px;
        }
        
        .download-btn {
            background-color: #BAFC51;
            color: #000;
            padding: 15px 30px;
            border-radius: 30px;
            text-decoration: none;
            font-weight: bold;
            font-size: 18px;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 10px;
        }
        
        .download-btn:hover {
            background-color: #a8e045;
            transform: translateY(-2px);
        }
        
        .steps-section {
            padding: 80px 0;
            background-color: #111;
        }
        
        .steps-title {
            text-align: center;
            font-size: 3rem;
            font-weight: bold;
            margin-bottom: 20px;
        }
        
        .steps-subtitle {
            text-align: center;
            font-size: 1.2rem;
            color: rgba(255, 255, 255, 0.8);
            margin-bottom: 60px;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
        }
        
        .steps-container {
            display: flex;
            justify-content: space-around;
            align-items: center;
            flex-wrap: wrap;
            gap: 40px;
        }
        
        .step {
            text-align: center;
            max-width: 300px;
        }
        
        .step-number {
            background-color: #BAFC51;
            color: #000;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            font-weight: bold;
            margin: 0 auto 20px;
        }
        
        .step h3 {
            font-size: 1.5rem;
            margin-bottom: 15px;
        }
        
        .step p {
            color: rgba(255, 255, 255, 0.8);
            line-height: 1.6;
        }
        
        .features-section {
            padding: 80px 0;
            background-color: #000;
        }
        
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 40px;
            margin-top: 60px;
        }
        
        .feature-card {
            background-color: #1a1a1a;
            padding: 30px;
            border-radius: 15px;
            border: 1px solid #262626;
        }
        
        .feature-card h3 {
            color: #BAFC51;
            font-size: 1.5rem;
            margin-bottom: 15px;
        }
        
        .feature-card p {
            color: rgba(255, 255, 255, 0.8);
            line-height: 1.6;
        }
        
        .footer {
            background-color: #111;
            padding: 60px 0 30px;
            border-top: 1px solid #262626;
        }
        
        .footer-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 40px;
            margin-bottom: 30px;
        }
        
        .footer-section h4 {
            color: #BAFC51;
            margin-bottom: 20px;
            font-size: 1.2rem;
        }
        
        .footer-section p, .footer-section a {
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            margin-bottom: 10px;
            display: block;
        }
        
        .footer-section a:hover {
            color: #BAFC51;
        }
        
        .footer-bottom {
            text-align: center;
            padding-top: 30px;
            border-top: 1px solid #262626;
            color: rgba(255, 255, 255, 0.5);
        }
        
        @media (max-width: 768px) {
            .hero-title {
                font-size: 2.5rem;
            }
            
            .hero-subtitle {
                font-size: 1.2rem;
            }
            
            .steps-title {
                font-size: 2rem;
            }
            
            .header-content {
                flex-direction: column;
                gap: 20px;
            }
            
            .download-section {
                flex-direction: column;
                align-items: center;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo-section">
                    <div class="logo">
                        <img src="./static/image/logo3x.png" alt="FunShot Logo">
                    </div>
                    <div class="app-info">
                        <h1>Funshot App</h1>
                        <p>Video 'New' Generation</p>
                    </div>
                </div>
                <a href="https://linktr.ee/funshot_io" class="contact-btn" target="_blank">Contact Us</a>
            </div>
        </div>
    </header>

    <main>
        <section class="hero-section">
            <div class="container">
                <h1 class="hero-title">
                    Incentive <span class="highlight">Worldwide</span>
                </h1>
                <p class="hero-subtitle">
                    Watch short videos, invite friends, and complete tasks on your mobile to earn F-Bean, easily participating in the Web3 value network.
                </p>
                
                <div class="download-section">
                    <a href="#" class="download-btn" onclick="handleDownload('android')">
                        📱 Download for Android
                    </a>
                    <a href="#" class="download-btn" onclick="handleDownload('ios')">
                        🍎 Download for iOS
                    </a>
                </div>

                <!-- 调试信息显示区域 -->
                <div id="debug-info" style="margin-top: 30px; padding: 20px; background: rgba(255,255,255,0.1); border-radius: 10px; font-family: monospace; font-size: 12px; text-align: left; max-width: 600px; margin-left: auto; margin-right: auto; display: none;">
                    <h4 style="color: #BAFC51; margin-bottom: 10px;">调试信息:</h4>
                    <div id="debug-content" style="max-height: 300px; overflow-y: auto; margin-bottom: 10px;"></div>
                    <button onclick="document.getElementById('debug-content').innerHTML=''; debugLog('调试日志已清空')" style="margin-right: 10px; padding: 5px 10px; background: #666; color: white; border: none; border-radius: 5px; cursor: pointer;">清空日志</button>
                    <button onclick="document.getElementById('debug-info').style.display='none'" style="padding: 5px 10px; background: #BAFC51; color: #000; border: none; border-radius: 5px; cursor: pointer;">关闭</button>
                </div>

                <!-- 测试按钮区域 -->
                <div style="margin-top: 20px;">
                    <button onclick="debugLog('手动测试开始'); handleDownload('android')" style="margin: 5px; padding: 8px 15px; background: #333; color: white; border: 1px solid #666; border-radius: 5px; cursor: pointer;">🔧 测试安卓下载</button>
                    <button onclick="debugLog('显示调试信息'); document.getElementById('debug-info').style.display='block'" style="margin: 5px; padding: 8px 15px; background: #333; color: white; border: 1px solid #666; border-radius: 5px; cursor: pointer;">📊 显示调试</button>
                </div>
            </div>
        </section>

        <section class="steps-section">
            <div class="container">
                <h2 class="steps-title">Start Your Funshot Journey</h2>
                <p class="steps-subtitle">
                    Just 3 steps to experience exciting new ways to enjoy short dramas and earn rewards. Watch shows, complete tasks, invite friends, and earn F-Bean every day!
                </p>
                
                <div class="steps-container">
                    <div class="step">
                        <div class="step-number">01</div>
                        <h3>Create an Account</h3>
                        <p>Sign up quickly and easily to start your journey with FunShot</p>
                    </div>
                    
                    <div class="step">
                        <div class="step-number">02</div>
                        <h3>Start Watching</h3>
                        <p>Enjoy premium short dramas and videos from around the world</p>
                    </div>
                    
                    <div class="step">
                        <div class="step-number">03</div>
                        <h3>Earn F-Bean</h3>
                        <p>Get rewarded for watching, sharing, and inviting friends</p>
                    </div>
                </div>
            </div>
        </section>

        <section class="features-section">
            <div class="container">
                <h2 class="steps-title">Why Choose FunShot?</h2>
                
                <div class="features-grid">
                    <div class="feature-card">
                        <h3>🎬 Free Global Short Dramas</h3>
                        <p>Access premium content from Europe, America, Japan, and Korea. No membership needed, no ads, truly free entertainment.</p>
                    </div>
                    
                    <div class="feature-card">
                        <h3>⚽ Neymar Global Ambassador</h3>
                        <p>Global soccer star Neymar Jr. officially endorses FunShot, enhancing our international brand presence.</p>
                    </div>
                    
                    <div class="feature-card">
                        <h3>🎯 TikTok-Level Video Ecosystem</h3>
                        <p>Millions of creators producing short dramas, vlogs, tutorials, games, beauty, food, fitness content and more.</p>
                    </div>
                    
                    <div class="feature-card">
                        <h3>💰 Unique F-Bean Earning System</h3>
                        <p>Earn F-Bean tokens through watching, sharing, and creating. A deflationary model with real value and multiple use cases.</p>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h4>About Us</h4>
                    <p>Funshot Introduction</p>
                    <p>User Rights and Safety</p>
                </div>
                
                <div class="footer-section">
                    <h4>Ecosystem Links</h4>
                    <p>F-Bean Mechanism</p>
                    <p>Influencer Cooperation Plan</p>
                </div>
                
                <div class="footer-section">
                    <h4>Follow Us</h4>
                    <a href="https://linktr.ee/funshot_io" target="_blank">Twitter</a>
                    <a href="https://linktr.ee/funshot_io" target="_blank">Instagram</a>
                </div>
            </div>
            
            <div class="footer-bottom">
                <p>Privacy Policy | Copyright © 2025 Funshot</p>
            </div>
        </div>
    </footer>

    <script>
        // 调试日志函数
        function debugLog(message, data = null) {
            console.log(message, data);
            const debugContent = document.getElementById('debug-content');
            const debugInfo = document.getElementById('debug-info');

            if (debugContent) {
                const timestamp = new Date().toLocaleTimeString();
                const logEntry = document.createElement('div');
                logEntry.style.marginBottom = '5px';
                logEntry.innerHTML = `<span style="color: #BAFC51;">[${timestamp}]</span> ${message}`;
                if (data) {
                    logEntry.innerHTML += `<br><span style="color: #8E8E92; margin-left: 20px;">${JSON.stringify(data, null, 2)}</span>`;
                }
                debugContent.appendChild(logEntry);
                debugInfo.style.display = 'block';

                // 自动滚动到最新日志
                debugContent.scrollTop = debugContent.scrollHeight;
            }
        }

        // 改进的下载处理函数
        function handleDownload(platform = 'auto') {
            debugLog('开始下载处理...', { platform });

            // 检测设备类型
            const userAgent = navigator.userAgent.toLowerCase();
            const isAndroid = userAgent.indexOf('android') > -1 || platform === 'android';
            const isIOS = (userAgent.indexOf('iphone') > -1 || userAgent.indexOf('ipad') > -1) || platform === 'ios';
            const isMobile = /android|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(userAgent);

            debugLog('设备检测结果', {
                userAgent,
                isAndroid,
                isIOS,
                isMobile,
                platform,
                screenWidth: window.screen.width,
                windowWidth: window.innerWidth
            });

            // 检查OpenInstall SDK状态
            const openInstallAvailable = typeof OpenInstall !== 'undefined';
            debugLog('OpenInstall SDK状态', {
                available: openInstallAvailable,
                type: typeof OpenInstall
            });

            // 尝试使用OpenInstall
            if (openInstallAvailable) {
                debugLog('尝试使用OpenInstall SDK');
                try {
                    var data = OpenInstall.parseUrlParams();
                    debugLog('OpenInstall URL参数解析', data);

                    new OpenInstall({
                        appKey: "l23zgl",
                        onready: function() {
                            debugLog('OpenInstall SDK 准备就绪');
                            var m = this;

                            // 先尝试唤醒应用
                            debugLog('尝试唤醒应用...');
                            m.schemeWakeup();

                            // 延迟执行安装，给唤醒一些时间
                            setTimeout(function() {
                                debugLog('执行应用安装/下载...');
                                m.wakeupOrInstall();
                            }, 1500);
                        },
                        onerror: function(error) {
                            debugLog('OpenInstall 发生错误', error);
                            fallbackDownload(isAndroid, isIOS, platform);
                        }
                    }, data);
                } catch (error) {
                    debugLog('OpenInstall 初始化失败', {
                        error: error.message,
                        stack: error.stack
                    });
                    fallbackDownload(isAndroid, isIOS, platform);
                }
            } else {
                debugLog('OpenInstall SDK 未加载，使用备用下载方案');
                fallbackDownload(isAndroid, isIOS, platform);
            }
        }

        // 备用下载方案
        function fallbackDownload(isAndroid, isIOS, platform) {
            debugLog('执行备用下载方案', { isAndroid, isIOS, platform });

            if (isAndroid) {
                // 安卓备用下载链接
                const androidUrls = [
                    'https://play.google.com/store/apps/details?id=com.funshot.app', // Google Play
                    'https://f-s.fun/download/funshot.apk', // 直接APK下载
                    'https://github.com/funshot/releases/latest/download/funshot.apk', // GitHub备用
                    'https://apkpure.com/funshot/com.funshot.app', // APKPure
                    'https://m.apkpure.com/funshot/com.funshot.app' // APKPure Mobile
                ];

                debugLog('准备尝试安卓下载链接', androidUrls);
                tryMultipleUrls(androidUrls, 'Android');

            } else if (isIOS) {
                debugLog('执行iOS下载');
                // iOS下载
                const iosUrl = 'https://apps.apple.com/app/funshot/id123456789';
                window.open(iosUrl, '_blank');

                // 显示iOS下载提示
                setTimeout(() => {
                    showIOSDownloadTip();
                }, 1000);

            } else {
                debugLog('桌面端访问，显示二维码');
                // 桌面端，显示二维码或引导
                showQRCode();
            }
        }

        // 尝试多个URL
        function tryMultipleUrls(urls, platform) {
            debugLog(`尝试 ${platform} 下载链接`, urls);

            // 首先尝试第一个链接
            const primaryUrl = urls[0];
            debugLog('尝试主要下载链接', { url: primaryUrl });

            // 创建隐藏的下载链接
            const link = document.createElement('a');
            link.href = primaryUrl;
            link.download = platform === 'Android' ? 'funshot.apk' : '';
            link.target = '_blank';
            link.style.display = 'none';

            // 添加到页面并点击
            document.body.appendChild(link);

            try {
                link.click();
                debugLog('下载链接点击成功');
            } catch (error) {
                debugLog('下载链接点击失败', error);
            }

            document.body.removeChild(link);

            // 显示备用选项
            setTimeout(() => {
                debugLog('显示备用下载选项');
                showDownloadOptions(urls, platform);
            }, 3000);
        }

        // 显示下载选项
        function showDownloadOptions(urls, platform) {
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.8);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 10000;
            `;

            const content = document.createElement('div');
            content.style.cssText = `
                background: #1a1a1a;
                padding: 30px;
                border-radius: 15px;
                border: 1px solid #262626;
                max-width: 400px;
                width: 90%;
                text-align: center;
            `;

            content.innerHTML = `
                <h3 style="color: #BAFC51; margin-bottom: 20px;">选择下载方式</h3>
                <p style="color: white; margin-bottom: 20px;">如果下载没有开始，请选择以下方式：</p>
                ${urls.map((url, index) => `
                    <button onclick="window.open('${url}', '_blank')"
                            style="display: block; width: 100%; margin: 10px 0; padding: 12px;
                                   background: #BAFC51; color: #000; border: none; border-radius: 8px;
                                   font-weight: bold; cursor: pointer;">
                        下载方式 ${index + 1}
                    </button>
                `).join('')}
                <button onclick="this.parentElement.parentElement.remove()"
                        style="margin-top: 15px; padding: 8px 20px; background: transparent;
                               color: #8E8E92; border: 1px solid #8E8E92; border-radius: 8px; cursor: pointer;">
                    关闭
                </button>
            `;

            modal.appendChild(content);
            document.body.appendChild(modal);
        }

        // 显示iOS下载提示
        function showIOSDownloadTip() {
            const modal = createModal();
            const content = modal.querySelector('.modal-content');

            content.innerHTML = `
                <h3 style="color: #BAFC51; margin-bottom: 20px;">iOS 下载说明</h3>
                <p style="color: white; margin-bottom: 20px;">如果App Store没有自动打开，请：</p>
                <ol style="color: white; text-align: left; margin-bottom: 20px;">
                    <li>打开App Store应用</li>
                    <li>搜索 "FunShot"</li>
                    <li>点击下载安装</li>
                </ol>
                <button onclick="window.open('https://apps.apple.com/app/funshot/id123456789', '_blank')"
                        style="display: block; width: 100%; margin: 10px 0; padding: 12px;
                               background: #BAFC51; color: #000; border: none; border-radius: 8px;
                               font-weight: bold; cursor: pointer;">
                    重新打开App Store
                </button>
                <button onclick="this.parentElement.parentElement.remove()"
                        style="margin-top: 15px; padding: 8px 20px; background: transparent;
                               color: #8E8E92; border: 1px solid #8E8E92; border-radius: 8px; cursor: pointer;">
                    关闭
                </button>
            `;
        }

        // 显示二维码
        function showQRCode() {
            debugLog('显示二维码下载选项');

            const modal = createModal();
            const content = modal.querySelector('.modal-content');

            content.innerHTML = `
                <h3 style="color: #BAFC51; margin-bottom: 20px;">手机下载</h3>
                <p style="color: white; margin-bottom: 20px;">请使用手机访问以下方式下载：</p>
                <div style="margin: 20px 0;">
                    <p style="color: #BAFC51; margin-bottom: 10px;">方式1: 手机浏览器访问</p>
                    <p style="background: #333; padding: 10px; border-radius: 5px; font-family: monospace; word-break: break-all;">
                        ${window.location.href}
                    </p>
                </div>
                <div style="margin: 20px 0;">
                    <p style="color: #BAFC51; margin-bottom: 10px;">方式2: 扫描二维码</p>
                    <div style="background: white; padding: 20px; border-radius: 10px; margin: 10px 0;">
                        <div style="color: #000; text-align: center; font-size: 48px;">📱</div>
                        <p style="color: #000; text-align: center; margin: 10px 0;">二维码占位符</p>
                        <p style="color: #666; text-align: center; font-size: 12px;">请使用手机扫描此二维码</p>
                    </div>
                </div>
                <button onclick="this.parentElement.parentElement.remove()"
                        style="margin-top: 15px; padding: 8px 20px; background: #BAFC51; color: #000;
                               border: none; border-radius: 8px; cursor: pointer;">
                    关闭
                </button>
            `;
        }

        // 创建模态框的通用函数
        function createModal() {
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.8);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 10000;
            `;

            const content = document.createElement('div');
            content.className = 'modal-content';
            content.style.cssText = `
                background: #1a1a1a;
                padding: 30px;
                border-radius: 15px;
                border: 1px solid #262626;
                max-width: 500px;
                width: 90%;
                text-align: center;
                max-height: 80vh;
                overflow-y: auto;
            `;

            modal.appendChild(content);
            document.body.appendChild(modal);

            return modal;
        }

        // 备用下载方案
        function fallbackDownload(isAndroid, isIOS) {
            console.log('执行备用下载方案');

            if (isAndroid) {
                // 安卓备用下载链接
                const androidUrls = [
                    'https://play.google.com/store/apps/details?id=com.funshot.app', // Google Play
                    'https://f-s.fun/download/funshot.apk', // 直接APK下载
                    'https://github.com/funshot/releases/latest/download/funshot.apk' // GitHub备用
                ];

                // 尝试多个下载源
                tryMultipleUrls(androidUrls, 'Android');

            } else if (isIOS) {
                // iOS下载
                window.open('https://apps.apple.com/app/funshot/id123456789', '_blank');

            } else {
                // 桌面端，显示二维码或引导
                showQRCode();
            }
        }

        // 尝试多个URL
        function tryMultipleUrls(urls, platform) {
            console.log(`尝试 ${platform} 下载链接:`, urls);

            // 首先尝试第一个链接
            const primaryUrl = urls[0];
            const link = document.createElement('a');
            link.href = primaryUrl;
            link.download = 'funshot.apk';
            link.target = '_blank';

            // 添加到页面并点击
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            // 显示备用选项
            setTimeout(() => {
                showDownloadOptions(urls, platform);
            }, 2000);
        }

        // 显示下载选项
        function showDownloadOptions(urls, platform) {
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.8);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 10000;
            `;

            const content = document.createElement('div');
            content.style.cssText = `
                background: #1a1a1a;
                padding: 30px;
                border-radius: 15px;
                border: 1px solid #262626;
                max-width: 400px;
                width: 90%;
                text-align: center;
            `;

            content.innerHTML = `
                <h3 style="color: #BAFC51; margin-bottom: 20px;">选择下载方式</h3>
                <p style="color: white; margin-bottom: 20px;">如果下载没有开始，请选择以下方式：</p>
                ${urls.map((url, index) => `
                    <button onclick="window.open('${url}', '_blank')"
                            style="display: block; width: 100%; margin: 10px 0; padding: 12px;
                                   background: #BAFC51; color: #000; border: none; border-radius: 8px;
                                   font-weight: bold; cursor: pointer;">
                        下载方式 ${index + 1}
                    </button>
                `).join('')}
                <button onclick="this.parentElement.parentElement.remove()"
                        style="margin-top: 15px; padding: 8px 20px; background: transparent;
                               color: #8E8E92; border: 1px solid #8E8E92; border-radius: 8px; cursor: pointer;">
                    关闭
                </button>
            `;

            modal.appendChild(content);
            document.body.appendChild(modal);
        }


        
        // 检测设备类型并添加相应的类名
        function checkDevice() {
            const width = window.innerWidth;
            const body = document.body;
            
            if (width < 960) {
                body.classList.add('mobile');
            } else {
                body.classList.add('pc');
            }
        }
        
        // 页面加载时检测设备
        window.addEventListener('load', checkDevice);
        window.addEventListener('resize', checkDevice);
    </script>
</body>
</html>
