import Autoplay from './autoplay/autoplay'
import Pagination from './pagination/pagination'
import Navigation from './navigation/navigation'
import Scrollbar from './scrollbar/scrollbar'
import Thumb from './thumbs/thumbs'
import EffectFade from './effect-fade/effect-fade'
import EffectCube from './effect-cube/effect-cube'
import EffectCoverflow from './effect-coverflow/effect-coverflow'
import EffectFlip from './effect-flip/effect-flip'
import EffectCards from './effect-cards/effect-cards'
import EffectCreative from './effect-creative/effect-creative'
import FreeMode from './free-mode/free-mode'
import Controller from './controller/controller'
import Virtual from './virtual/virtual'
// import Zoom from './zoom/zoom'

export {
  Autoplay,
  Pagination,
  Navigation,
  Scrollbar,
  Thumb,
  EffectFade,
  EffectCube,
  EffectCoverflow,
  EffectFlip,
  EffectCards,
  EffectCreative,
  FreeMode,
  Controller,
  Virtual
  //   Zoom
}

export type {
  AutoplayOptions,
  AutoplayMethods,
  AutoplayEvents
} from '../types/modules/autoplay'

export type {
  PaginationOptions,
  PaginationMethods,
  PaginationEvents
} from '../types/modules/pagination'

export type {
  NavigationOptions,
  NavigationMethods
} from '../types/modules/navigation'

export type {
  ScrollbarOptions,
  ScrollbarMethods
} from '../types/modules/scrollbar'

export type {
  ThumbsOptions,
  ThumbsMethods,
  ThumbsEvents
} from '../types/modules/thumbs'

export type {
  FadeEffectOptions,
  FadeEffectMethods
} from '../types/modules/effect-fade'

export type {
  CubeEffectOptions,
  CubeEffectMethods
} from '../types/modules/effect-cube'

export type {
  CoverflowEffectOptions,
  CoverflowEffectMethods
} from '../types/modules/effect-coverflow'

export type {
  FlipEffectOptions,
  FlipEffectMethods
} from '../types/modules/effect-flip'

export type {
  CardsEffectOptions,
  CardsEffectMethods
} from '../types/modules/effect-cards'

export type {
  CreativeEffectOptions,
  CreativeEffectMethods
} from '../types/modules/effect-creative'

export type {
  FreeModeOptions,
  FreeModeMethods
} from '../types/modules/free-mode'

export type {
  ControllerOptions,
  ControllerMethods
} from '../types/modules/controller'

export type {
  VirtualOptions,
  VirtualMethods,
  VirtualEvents,
  VirtualData
} from '../types/modules/virtual'

export type { ZoomOptions, ZoomMethods } from '../types/modules/zoom'
