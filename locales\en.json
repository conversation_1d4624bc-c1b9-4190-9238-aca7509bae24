{
	"logotit": "Funshot App",
	"logotitmes": "Video 'New' Generation",
	"learn": "Learn More", //***新加
	"header": {
		"Overview": "Overview",
		"Performance": "Performance",
		"Features": "Features",
		"Advantages": "Advantages",
		"Join": "Join Us",
		"Download": "Download App"
	},
	"lang": {
		"Indonesian": "Indonesian",
		"Eenglish": "Eenglish",
		// "German": "German",
		// "French": "French",
		// "Spanish": "Spanish",
		// "Turkish": "Turkish",
		// "Korean": "Korean",
		// "Japanese": "Japanese",
		"Chinese": "Chinese"
	},
	"dhmenu": [{
			"tit": "About Us",
			"mes1": "Funshot Introduction",
			"mes2": "User Rights and Safety"
		},
		{
			"tit": "Ecosystem Links",
			"mes1": "F-Bean Mechanism",
			"mes2": "Influencer Cooperation Plan"
		},
		{
			"tit": "Please enter your email address",
			"mes1": "Subscribe",
			"mes2": "Receive the latest Funshot updates, product features, incentive mechanisms, and platform activity previews."
		}
	],
	"FollowUs": "Follow Us",
	"ContactUs": "Contact Us",
	"Support": "Get More Support from Funshot",
	"ecosystem": "Join the Funshot creator and partner ecosystem to participate in short video content sharing and ecosystem incentives.",
	"pcBanner": {
		"banner1": {
			"Incentive": "Incentive",
			"Worldwide": "Worldwide",
			"msg": "A new generation \"Watch, Shoot, Earn\" short video platform, enabling every user toparticipatein content sharing and value growth."
		},
		"banner2": {
			"Incentive": "Incentive",
			"Worldwide": "Worldwide",
			"msg": "Funshot x Neymar Jr. Officially announced as the global ambassador, inspiring gameplay worldwide."
		},
		"banner3": {
			"First": "The First",
			"Watch": "“Watch-and-Earn”",
			"Platform": "Platform Growth Guide",
			"msg": "Content × Incentives × Viral Loops — A Full-Funnel Strategy to Ignite Emerging Markets and Drive DAU & Profit Growth for FunShot"
		},
		"banner4": {
			"Why": "Why",
			"FunShot": "FunShot, Why Now?",
			"msg": "Southeast Asia is witnessing a massive shift from attention to income — FunShot is the first to capture it."
		}
	},
	"upgrades": {
		"tit1": "Funshot's",
		"tit2": "Four Major Feature Upgrades",
		"tit3": "Content Matrix + Interactive Mechanism + Global Incentives",
		"leftchose": [{
				"ltit": "Free Global Short Dramas",
				"rtit": "Free Short Dramas",
				"rmes": "Funshot collaborates with international content providers to create an exclusive short drama section, covering popular IPs from Europe, America, Japan, and Korea.From light suspense to high-energy dramas, each is finely edited for high-frequency pacing, meeting the dual needs of \"binge-watching dramas + short videos.\"No membership needed, no ads, truly free to watch anytime, anywhere, with sharing capabilities."
			},
			{
				"ltit": "Neymar as Global Ambassador",
				"rtit": "Neymar Global Ambassador",
				"rmes": "Global soccer star Neymar Jr. officially endorses Funshot, enhancing the platform's international brand image. His presence will feature in the app's launch page, video content, and promotional materials, strengthening Funshot's appeal among young users and international social communities. Known as the \"short video app Neymar uses,\" it naturally becomes a social media conversation starter."
			},
			{
				"ltit": "TikTok Video Ecosystem",
				"rtit": "Video Ecosystem",
				"rmes": "Funshot rivals TikTok with its content distribution and video quality, gathering millions of creators. It covers:\n·Short dramas, vlogs, tutorials, games, beauty, food, fitness, and more.\n·AI-driven personalized recommendations for better user understanding.\n·An innovative \"Plaza Hall\" for interactive content-based socializing.\n·More than a short video platform, it's a dynamic social space."
			},
			{
				"ltit": "Unique Earning Mechanism",
				"rtit": "Global F-Bean Incentives",
				"rmes": "Funshot has developed a diverse earnings system with the \"F-Bean\" token, creating a new economy where actions have value. Users earn F-Beans for positive actions like watching videos, inviting friends, and more. Notably, the platform includes a Rune system that boosts earnings, enabling continuous growth. With a capped supply, daily reduction, and no pre-mining, early participants gain more benefits at lower costs."
			}
		]
	},
	"aliexpress": {
		"tit1": "Whether global trends or local consumer waves, Funshot can become a broader cultural platform.",
		"tit2": "AliExpress Europe Regional Director"
	},
	"funshot": {
		"tit1": "Funshot",
		"tit2": "is a collaborative short video incentive platform",
		"flfdata": [{
				"tit": "Driven by Revenue Sharing",
				"mes": "Encouraging participation and growth, Funshot uses \"watch videos to earn F-Beans and invite friends for rewards\" as its core mechanism, creating a user-centric short video platform ecosystem."
			},
			{
				"tit": "User Co-Creation",
				"mes": "Earn while watching, share for rewards. Incentives cover viewing, creating, and inviting actions, achieving mutual value for the platform and users."
			},
			{
				"tit": "Continuous income",
				"mes": "Earnings continuously accumulate with permanent invitation binding, ensuring long-term, non-diminishing income."
			}
		]
	},
	"fullxz": [{
			"toptit": "Full-chain short video growth support",
			"xzmes": "Creative Ideation",
			"tit1": "Find your next growth breakthrough",
			"tit2": "Funshot Growth Support Center",
			"mes": "invitation systems, task incentives, and creative content. Offers tools and data interfaces to help brands achieve rapid expansion and community engagement."
		},
		{
			"toptit": "Full-chain short video growth support",
			"xzmes": "Content Production",
			"tit1": "Find your next popular video",
			"tit2": "Funshot Content Creation Center",
			"mes": "Discover your next hit video with a vast array of official templates, popular subtitle styles, and sound effects. Our platform integrates content incentive mechanisms and tagging systems to help creators efficiently produce engaging video content."
		},
		{
			"toptit": "Full-chain short video growth support",
			"xzmes": "Invitation & Growth",
			"tit1": "The lever for growth lies within relationship networks.",
			"tit2": "Funshot Growth Toolbox",
			"mes": "Leverage relationship networks to drive growth. Through the F-Bean incentive system, team dynamics, and unique invitation codes, users are motivated to recruit, share, and form teams, creating a sustainable growth cycle."
		},
		{
			"toptit": "Full-chain short video growth support",
			"xzmes": "Data Growth",
			"tit1": "Growth isn't mystical; it's a quantifiable long-term strategy.",
			"tit2": "Funshot Data Insight System",
			"mes": "Growth isn't mystical; it's quantifiable. The system features dashboards for real-time tracking of key metrics like new users, activity, and F-Bean production. It combines individual and team perspectives to help users and creators optimize their growth strategies."
		}
	],
	"Ultimate": {
		"tit1": "Funshot's",
		"tit2": "Ultimate Vision",
		"tit3": "· More than just watching videos, it's about co-creating value. Funshot aims to create a platform for user growth, monetization, and collaboration.",
		"vision": [{
				"tit1": "Platform Vision",
				"mes": "Funshot is a short video platform that combines content consumption, social interaction, and F-Bean incentives. It serves as the starting point for value flow, not just entertainment. Users earn F-Bean rewards by watching videos,inviting friends, and completing tasks. The platform also includes features like user teams, level systems, and creative task incentives to help users convert their actions into value, becoming partners in the platform's growth.",
				"tit2": "Core Advantages",
				"mm1": "Everyone can participate, earn while playing, and easily own digital assets.",
				"mm2": "Content consumption is like mining; the more you watch, the more you earn.",
				"mm3": "Sustainable revenue model through new users, tasks, and inscriptions.",
				"mm4": "Long-term locked earnings, F-Bean has a deflationary model, and the incentive mechanism is transparent and traceable."
			},
			{
				"tit1": "F-Bean Introduction",
				"mes": "Users earn \"F-Bean\" rewards by watching videos, inviting friends, posting content, and equipping inscriptions. This \"earn while you engage\" model allows everyone to contribute to content circulation and platform growth.F-Bean has a deflationary issuance mechanism with daily reductions and a capped total supply. It can be used for exchanging red packets, membership privileges, and governance votes. Future applications include ad revenue sharing, inscription trading, and ecosystem points.",
				"tit2": "Key Features:",
				"mm1": "Earn F-Bean easily by watching videos, inviting new users, and completing tasks.",
				"mm2": "Early participation provides advantages with increased earnings.",
				"mm3": "Transparent incentive mechanism with real value support.",
				"mm4": "Multiple application scenarios including withdrawal, trading, and ecosystem voting."
			},
			{
				"tit1": "Team Reward System",
				"mes": "Funshot introduces a profit-sharing system based on \"real-name invitations + team collaboration.\" Once friends complete real-name verification, team rewards begin.Each verified friend continuously generates F-Bean for you, along with benefits like withdrawal fee reductions and membership level upgrades.",
				"tit2": "Core Highlights",
				"mm1": "More real-name invitations lead to higher base earnings and extra F-Bean rewards.",
				"mm2": "Greater team activity increases contribution value and profit-sharing multiplier.",
				"mm3": "Winning teams share more prizes and F-Bean rewards.",
				"mm4": "Earnings structure is trackable, with a clear and transparent incentive mechanism."
			}
		]
	},
	"revenue": {
		"tit1": "Funshot's",
		"tit2": "Unique Revenue Model",
		"tit3": "Starting with content, gathering in social circles, winning with motivation, and giving back to the platform"
	},
	"platform": {
		"tit1": "Creators Flooding the Platform",
		"tit2": "Short films, lifestyle, beauty, and fashion influencers are joining in droves."
		// "tit3": "Starting with content, gathering in social circles, winning with motivation, and giving back to the platform"
	},
	"team": {
		"tit1": "Funshot",
		"tit2": "Elite Team",
		"tit3": "Content and tech experts from Harvard, Yale, Berkeley, and Imperial College collaborate to create the next-generation global co-creative short video platform.",
		"teamtab": [{
				"name": "Ethan",
				"job": "CEO",
				"mes1": "Harvard PhD, former Apple Strategic Investment Director, co-founder of a Web3 startup. Experienced in content platform funding. Founded Funshot to create a decentralized content ecosystem where everyone participates and benefits.",
				"mes2": "Keywords: Platform Strategy / Incentive Model / Web3 Content Economy",
				"Followers": "Followers"
			},
			{
				"name": "Ken",
				"job": "CTO",
				"mes1": "MIT Computer Science graduate, former ByteDance tech architect. Expert in blockchain architecture and high concurrency systems. Designed Funshot F-Bean incentive mechanism and growth model.",
				"mes2": "Keywords: Web3 System Design / Deflationary Mechanism / Distributed Computing Architecture",
				"Followers": "Followers"
			},
			{
				"name": "Lisa",
				"job": "CCO",
				"mes1": "Yale Journalism and Communication graduate, former TikTok International Content Operations Director. Led growth strategies for TikTok drama channels. Skilled in content matrix,storytelling, and incentive mechanisms.",
				"mes2": "Keywords: Drama Content Planning / Global Creator Ecosystem / Content Commercialization",
				"Followers": "Followers"
			},
			{
				"name": "Alex",
				"job": "CGO",
				"mes1": "Stanford Marketing and Media Management graduate, former TikTok for Business Global Partnerships VP. Managed Funshot × Neymar global endorsement and multi-country market launches.",
				"mes2": "Keywords: Global Strategy / Celebrity Collaboration / Channel Expansion & BD",
				"Followers": "Followers"
			}
		]
	},
	"join": {
		"tit1": "Join",
		"tit2": "Us",
		"tit3": "Want to be among the first to monetize content? Contact us and join the Funshot creator community.",
		"community": "Join our community",
		"disharmony": "disharmony",
		"telegram": "telegram",
		"formtype": "Form Type",
		"items": [{
				"name": "Content Creator / MCN"
			},
			{
				"name": "Channel / Agent"
			},
			{
				"name": "Brand Advertiser"
			},
			{
				"name": "Investor"
			},
			{
				"name": "General User / Other"
			}
		],
		"nextStep": "Next Step"
	},
	"down": {
		"homepage": "homepage",
		"tit1": "Incentive",
		"tit2": "Worldwide",
		"tit3": "Watch short videos, invite friends, and complete tasks on your mobile to earn F-Bean, easily participating in the Web3 value network.",
		"Journey": {
			"tit1": "Start Your Funshot Journey",
			"tit2": "Just 3 steps to experience exciting new ways to enjoy short dramas and earn rewards. Watch shows, complete tasks, invite friends, and earn F-Bean every day!",
			"stepdata": [{
					"tit1": "Step 01",
					"tit2": "Create an Account"
				},
				{
					"tit1": "Step 02",
					"tit2": "Start Watching"
				},
				{
					"tit1": "Step 03",
					"tit2": "Earn F-Bean"
				}
			],
			"bean": [{
					"tit1": "Watch F-Bean Production",
					"tit2": "Users can automatically earn F-Bean rewards by watching short dramas, completing tasks, and sharing interactions, easily participating in the platform's incentive cycle."
				},
				{
					"tit1": "F-Bean Dual Benefit",
					"tit2": "Creators can earn F-Bean revenue through their works, and the platform rewards higher returns based on user activity, achieving win-win growth."
				}
			]
		}
	},
	// 新
	"full": [{
			"tit": "Discover Great Shows",
			"mes": "Personalized recommendations help users explore trending short dramas, fun challenges, and celebrity content. Daily updates ensure everyone finds content they love."
		},
		{
			"tit": "User Engagement",
			"mes": "Earn Mi Beans by watching shows, completing tasks, and interacting. The more active you are, the higher your rewards and level."
		},
		{
			"tit": "Viral Growth",
			"mes": "Share invite codes or team up to spread the platform. Turn viewers into promoters for organic growth."
		},
		{
			"tit": "Boost Retention",
			"mes": "Daily Mi Bean rewards, level badges, and time-limited tasks keep users active. Build a growth loop: content → incentives → sharing → new content."
		}
	],
	// 新
	"appbot": {
		"FunshotApp": "Funshot App",
		"Generation": "Video 'New' Generation",
		"list": [{
				"tit1": "About Us",
				"tit2": "Funshot Introduction",
				"tit3": "User Rights and Safety"
			},
			{
				"tit1": "Ecosystem Links",
				"tit2": "F-Bean Mechanism",
				"tit3": "Influencer Cooperation Plan"
			},
			{
				"tit1": "Follow Us",
				"tit2": "twitter",
				"tit3": "insgram"
			}
		],
		"Privacy": "Privacy Policy",
		"Copyright": "Copyright © 2025 Funshot"
	}
}