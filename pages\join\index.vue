<template>
	<view class="content wrapper index_body" style="padding: 0;background-color: #000000;">
		<header class="header" v-if="ispc === true" style="padding-top: 60rpx;height: 230rpx;box-sizing: border-box;">
			<view class="header_box">
				<view class="logo_appname_boxbody">
					<view class="logo_appname_box" @click="menua(-1)">
						<view class="logo_box">
							<image src="../../static/image/logo3x.png" mode=""></image>
						</view>
						<view class="appname_box">
							<view class="appname">
								{{ $t('logotit') }}
							</view>
							<view class="log_tit">
								{{ $t('logotitmes') }}
							</view>
						</view>
					</view>
				</view>
				<view class="header_menu_boxbody" @mouseleave="menul">
					<view class="header_menu_box" :class="menuactive === index ? 'active' : ''"
						v-for="(item,index) in menulist" :key="index" @click="menua(index)" @mouseenter="menum(index)">
						<view class="menu_tit">
							<view v-if="item !== 'Download'" class="">
								{{$t('header.' + item)}}
							</view>
							<view v-else class="hot_download">
								<view class="hotimg">
									<image src="../../static/image/Vector.png" mode=""></image>
								</view>
								<view class="">
									{{$t('header.' + item)}}
								</view>
							</view>
						</view>
						<view class="a_liner">

						</view>
					</view>
				</view>
				<view class="lang_contact_box">
					<view class="lang_box_body">
						<view class="lang_imgbox">
							<image src="../../static/image/SVG.png" mode=""></image>
						</view>
						<view class="lang_choose_boxbody">
							<view class="lang_mes" @click="showlangchange">
								{{$t(lan)}}
							</view>
							<view class="iconfont icon-xiala xiala">

							</view>
						</view>
						<view class="langchange_box_body" style="top: 180rpx;">
							<view class="lang_box" v-for="(item,index) in langlist" :key="index"
								@click="lanchange(item)">
								{{$t('lang.' + item)}}
							</view>
						</view>
					</view>
					<view class="contact_box" @click="gotowburl">
						<view class="zzc">

						</view>
						<view class="contxt">
							{{$t('ContactUs')}}
						</view>
					</view>
				</view>
			</view>
		</header>
		<view v-else class="appheader">
			<view class="left_apphaderlogo">
				<view class="logo_appbox">
					<image src="../../static/image/logo3x.png" mode=""></image>
				</view>
				<view class="appname_box">
					<view class="appname_t">
						{{$t('logotit')}}
					</view>
					<view class="appname_b">
						{{$t('logotitmes')}}
					</view>
				</view>
			</view>
			<view class="hotapp_box" @click="gotopage">
				<image src="../../static/image/Group 1533208842.png" mode=""></image>
			</view>
		</view>
		<!-- 		<view v-show="langch" class="langchange_box_body">
			<view class="lang_box" v-for="(item,index) in langlist" :key="index" @click="lanchange(item)">
				{{$t('lang.' + item)}}
			</view>
		</view> -->
		<view class="joinus_boxbody">
			<view class="j_leftimgbox">
				<!-- <image src="../../static/image/Background.png" mode=""></image> -->
				<view class="imgjbox">
					<image src="../../static/image/join/Background+Border.png" mode=""></image>
				</view>
				<view class="j_biconbox">
					<view class="j_biconb">
						<view class="biconbox">
							<image src="../../static/image/join/Group 1533213407.png" mode="heightFix"></image>
						</view>
						<view class="j_btit1">
							{{$t('join.community')}}
						</view>
						<view class="j_btit2">
							{{$t('join.disharmony')}}
						</view>
					</view>
					<view class="j_biconb" style="margin: 0;">
						<view class="biconbox">
							<image src="../../static/image/join/Group 1533213407.png" mode="heightFix"></image>
						</view>
						<view class="j_btit1">
							{{$t('join.community')}}
						</view>
						<view class="j_btit2">
							{{$t('join.telegram')}}
						</view>
					</view>
				</view>
			</view>
			<view class="right_form_boxbody">
				<view class="joinimgbox">
					<!-- <image src="../../static/image/Header.png" mode="widthFix"></image> -->
					<view class="joinust">
						<view class="" style="display: inline;color: #BAFC51;">
							{{$t('join.tit1')}}
						</view>
						{{$t('join.tit2')}}
					</view>
					<view class="joinmes">
						{{$t('join.tit3')}}
					</view>
				</view>
				<view class="xz_boxx_body">
					<view class="xzlefttit">
						<!-- <image src="../../static/image/Form Type_.png" mode="heightFix"></image> -->
						{{$t('join.formtype')}}
						<view class="" style="display: inline;color: #BAFC51;">
							*
						</view>
					</view>
					<view class="xzrightbox">
						<radio-group @change="radioChange">
							<label class="uni-list-cell uni-list-cell-pd" v-for="(item, index) in items"
								:key="item.value">
								<view>
									<radio :value="item.value" :checked="index === current"
										activeBackgroundColor="#000" />
								</view>
								<view>{{$t('join.items[' + index + '].name')}}</view>
							</label>
						</radio-group>
					</view>
				</view>
				<view class="joinbtn" @click="gotowburl">
					{{$t('join.nextStep')}}
				</view>
			</view>
		</view>
		<view class="bottom_joinbo">
			<!-- <image src="../../static/image/Footer.png" mode=""></image> -->
			<view class="dh_menu_boxbody">
				<view class="dhtit1">
					{{$t('dhmenu[0].tit')}}
				</view>
				<view class="dhtit2">
					{{$t('dhmenu[0].mes1')}}
				</view>
				<view class="dhtit2">
					{{$t('dhmenu[0].mes2')}}
				</view>
			</view>
			<view class="dh_menu_boxbody">
				<view class="dhtit1">
					{{$t('dhmenu[1].tit')}}
				</view>
				<view class="dhtit2">
					{{$t('dhmenu[1].mes1')}}
				</view>
				<view class="dhtit2">
					{{$t('dhmenu[1].mes2')}}
				</view>
			</view>
			<view class="dh_menu_boxbody">
				<view class="dhtit1">
					{{$t('FollowUs')}}
				</view>
				<view class="dhiconboxbody">
					<view class="dhicon" style="margin-right: 17px;" @click="gotowburl">
						<image src="../../static/image/join/Item.png" mode=""></image>
					</view>
					<view class="dhicon" @click="gotowburl">
						<image src="../../static/image/join/Item (1).png" mode=""></image>
					</view>
				</view>
			</view>
			<view class="Subscribe_boxbody">
				<view class="Subscribe_topbox">
					{{$t('dhmenu[2].tit')}}
					<view class="sub_box">
						{{$t('dhmenu[2].mes1')}}
					</view>
				</view>
				<view class="subtit">
					{{$t('dhmenu[2].mes2')}}
				</view>
			</view>
		</view>
	</view>
</template>

<script setup>
	import {
		onLoad,
		onReady
	} from '@dcloudio/uni-app';
	import {
		ref,
		reactive,
		onMounted,
		onBeforeMount,
	} from 'vue';

	import {
		useI18n
	} from 'vue-i18n';
	const {
		t,
		locale
	} = useI18n();

	const title = ref('hello')
	const menulist = ref(['Overview', 'Performance', 'Features', 'Advantages', 'Join', 'Download'])
	const menuactive = ref(4)
	const langlist = ref(['Indonesian', 'Eenglish', 'Chinese'])
	const langch = ref(false)

	const items = ref([{
			value: 'Content Creator / MCN',
			name: 'Content Creator / MCN',
		},
		{
			value: 'Channel / Agent',
			name: 'Channel / Agent'
		},
		{
			value: 'Brand Advertiser',
			name: 'Brand Advertiser'
		},
		{
			value: 'Investor',
			name: 'Investor'
		},
		{
			value: 'General User / Other',
			name: 'General User / Other'
		},
	])

	const viimg = ref(['../../static/image/vision/3.png', '../../static/image/vision/4.png',
		'../../static/image/vision/5.png'
	])

	const fullxz = ref([{
			toptit: 'Full-chain short video growth support',
			xzmes: 'Creative Ideation',
			url: '../../static/image/1 → SVG.png',
			tit1: 'Find your next growth breakthrough',
			tit2: 'Funshot Growth Support Center',
			mes: 'invitation systems, task incentives, and creative content. Offers tools and data interfaces to help brands achieve rapid expansion and community engagement.'
		},
		{
			toptit: 'Full-chain creative support',
			xzmes: 'Content Production',
			url: '../../static/image/4 → SVG.png',
			tit1: 'Find your next popular video',
			tit2: 'Funshot Content Creation Center',
			mes: 'Discover your next hit video with a vast array of official templates, popular subtitle styles, and sound effects. Our platform integrates content incentive mechanisms and tagging systems to help creators efficiently produce engaging video content.'
		},
		{
			toptit: 'Full-chain creative support',
			xzmes: 'Invitation & Growth',
			url: '../../static/image/2 → SVG.png',
			tit1: 'The lever for growth lies within relationship networks.',
			tit2: 'Funshot Growth Toolbox',
			mes: 'Leverage relationship networks to drive growth. Through the F-Bean incentive system, team dynamics, and unique invitation codes, users are motivated to recruit, share, and form teams, creating a sustainable growth cycle.'
		},
		{
			toptit: 'Full-chain creative support',
			xzmes: 'Data Growth',
			url: '../../static/image/3 → SVG.png',
			tit1: 'Growth isn' + 't mystical; it' + 's a quantifiable long-term strategy.',
			tit2: 'Funshot Data Insight System',
			mes: 'Growth isn' + 't mystical; it' +
				's quantifiable. The system features dashboards for real-time tracking of key metrics like new users, activity, and F-Bean production. It combines individual and team perspectives to help users and creators optimize their growth strategies.'
		}
	])
	const fullxzinx = ref(0)

	const learnchange = ref(['Smart Fix', 'Smart Creative'])
	const lchaninx = ref(0)

	const tb = ref(['TopView', 'BM'])
	const tbinx = ref(0)
	const flfdata = ref([{
			tit: 'Driven by Revenue Sharing',
			mes: 'Encouraging participation and growth, Funshot uses "watch videos to earn F-Beans and invite friends for rewards" as its core mechanism, creating a user-centric short video platform ecosystem.',
			num: '1587',
			dw: 'W+'
		},
		{
			tit: 'User Co-Creation',
			mes: 'Earn while watching, share for rewards. Incentives cover viewing, creating, and inviting actions, achieving mutual value for the platform and users.',
			num: '8587',
			dw: 'W+'
		},
		{
			tit: 'Continuous income',
			mes: 'Earnings continuously accumulate with permanent invitation binding, ensuring long-term, non-diminishing income.',
			num: '28.6',
			dw: 'Hour/day'
		}
	])
	const flfinx = ref(0)

	const alitab = ref([{
			url: '../../static/image/Item → Link.png'
		},
		{
			url: '../../static/image/Item → Link (1).png'
		},
		{
			url: '../../static/image/Item → Link (2).png'
		}
	])
	const aliinx = ref(0)

	const currentinx = ref(0)

	const leftchose = ref([{
			ltit: 'Free Global Short Dramas',
			rtit: 'Free Short Dramas',
			rmes: 'Funshot collaborates with international content providers to create an exclusive short drama section, covering popular IPs from Europe, America, Japan, and Korea.From light suspense to high-energy dramas, each is finely edited for high-frequency pacing, meeting the dual needs of "binge-watching dramas + short videos."No membership needed, no ads, truly free to watch anytime, anywhere, with sharing capabilities.',
			imgurl: '../../static/image/Background+Border.png'
		},
		{
			ltit: 'Neymar as Global Ambassador',
			rtit: 'Neymar Global Ambassador',
			rmes: 'Global soccer star Neymar Jr. officially endorses Funshot, enhancing the platform' +
				's international brand image. His presence will feature in the app' +
				's launch page, video content, and promotional materials, strengthening Funshot' +
				's appeal among young users and international social communities. Known as the ' +
				'short video app Neymar uses,' + 'it naturally becomes a social media conversation starter.',
			imgurl: '../../static/image/Background+Border1.png'
		},
		{
			ltit: 'TikTok Video Ecosystem',
			rtit: 'Video Ecosystem',
			rmes: 'Funshot rivals TikTok with its content distribution and video quality, gathering millions of creators. It covers:\n·Short dramas, vlogs, tutorials, games, beauty, food, fitness, and more.\n·AI-driven personalized recommendations for better user understanding.\n·An innovative "Plaza Hall" for interactive content-based socializing.\n·More than a short video platform, it' +
				's a dynamic social space.',
			imgurl: '../../static/image/Background+Border2.png'
		},
		{
			ltit: 'Unique Earning Mechanism',
			rtit: 'Global F-Bean Incentives',
			rmes: 'Funshot has developed a diverse earnings system with the "F-Bean" token, creating a new economy where actions have value. Users earn F-Beans for positive actions like watching videos, inviting friends, and more.Notably, the platform includes a Rune system that boosts earnings, enabling continuous growth. With a capped supply, daily reduction, and no pre-mining, early participants gain more benefits at lower costs.',
			imgurl: '../../static/image/Background+Border3.png'
		}
	])
	const lan = ref('lang.Eenglish')
	const ispc = ref(null)
	onBeforeMount(() => {
		if (uni.getStorageSync('lan')) {
			lan.value = 'lang.' + uni.getStorageSync('lan')
		}
		console.log(uni.getStorageSync('ispc'));
		ispc.value = uni.getStorageSync('ispc')
	})
	const leftchoseinx = ref(0)

	const lanchange = (item) => {
		console.log('lan', item);
		uni.setStorageSync('lan', item)
		if (item === 'Chinese') {
			uni.setStorageSync('lang', 'zh')
			location.reload();
		} else if (item === 'Eenglish') {
			uni.setStorageSync('lang', 'en')
			location.reload();
		} else {
			uni.setStorageSync('lang', 'id')
			location.reload();
		}

	}

	const menum = (index) => {
		menuactive.value = index
		// lastinx.value = index
	}
	const menul = () => {
		console.log('移出');
		// setTimeout(function() {
		// 	if (lastinx.value === menuactive.value) {
		menuactive.value = 4
		// 	}
		// }, 500)
	}


	const showlangchange = () => {
		langch.value = !langch.value
	}


	const gotowburl = () => {
		window.location.href = 'https://linktr.ee/funshot_io';
	}


	const menua = (index) => {
		menuactive.value = index
		if (index === 5) {
			uni.navigateTo({
				url: '/pages/download/index'
			})
		} else if (index === 4) {
			uni.navigateTo({
				url: '/pages/join/index'
			})
		} else if (index === -1) {
			uni.navigateTo({
				url: '/pages/index/index'
			})
		} else {
			uni.setStorageSync('menuainx', index)
			uni.navigateTo({
				url: '/pages/index/index'
			})
		}
	}
	const gotopage = () => {
		uni.navigateTo({
			url: '/pages/download/index'
		})
	}
	const leftcho = (index) => {
		leftchoseinx.value = index
	}
	const tbclick = (index) => {
		tbinx.value = index
	}
	const flfclick = (index) => {
		flfinx.value = index
	}
	const aliclick = (index) => {
		aliinx.value = index
	}
	const fullxzclick = (index) => {
		fullxzinx.value = index
	}
	const lanclick = (index) => {
		lchaninx.value = index
	}

	const swiperchang = (e) => {
		console.log('e', e.detail.current);
		currentinx.value = e.detail.current
	}
</script>

<style>

</style>

<style lang="scss">
	@import '../../static/scss/index.scss';

	.spswiper ::v-deep .uni-swiper-dots-horizontal {
		left: 10% !important;
	}

	.spswiper ::v-deep .uni-swiper-dot {
		width: 30rpx !important;
		height: 8rpx !important;
		border-radius: 6rpx !important;
	}

	.spswiper ::v-deep .uni-swiper-dot-active {
		width: 100rpx !important;
		background-color: #BAFC51;
	}

	.spswiper1 ::v-deep .uni-swiper-dot-active {
		// width: 100rpx !important;
		background-color: #BAFC51;
	}

	.botsscroll ::v-deep .uni-scroll-view-content {
		display: -webkit-inline-box !important;
		// flex-direction: row !important;
	}

	.uni-label-pointer {
		cursor: pointer;
		display: flex;
		flex-direction: row;
		align-items: center;
		margin-bottom: 20px;
	}
</style>