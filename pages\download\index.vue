<template>
	<view class="content wrapper index_body down_body" style="padding: 0;">
		<header class="header" v-if="ispc === true" style="padding-top: 60rpx;height: 230rpx;box-sizing: border-box;">
			<view class="header_box">
				<view class="logo_appname_boxbody">
					<view class="logo_appname_box" @click="menua(-1)">
						<view class="logo_box">
							<image src="../../static/image/logo3x.png" mode=""></image>
						</view>
						<view class="appname_box">
							<view class="appname">
								{{ $t('logotit') }}
							</view>
							<view class="log_tit">
								{{ $t('logotitmes') }}
							</view>
						</view>
					</view>
				</view>
				<view class="header_menu_boxbody" @mouseleave="menul">
					<view class="header_menu_box" :class="menuactive === index ? 'active' : ''"
						v-for="(item,index) in menulist" :key="index" @click="menua(index)" @mouseenter="menum(index)">
						<view class="menu_tit">
							<view v-if="item !== 'Download'" class="">
								{{$t('header.' + item)}}
							</view>
							<view v-else class="hot_download">
								<view class="hotimg">
									<image src="../../static/image/Vector.png" mode=""></image>
								</view>
								<view class="">
									{{$t('header.' + item)}}
								</view>
							</view>
						</view>
						<view class="a_liner">

						</view>
					</view>
				</view>
				<view class="lang_contact_box">
					<view class="lang_box_body">
						<view class="lang_imgbox">
							<image src="../../static/image/SVG.png" mode=""></image>
						</view>
						<view class="lang_choose_boxbody" @click="showlangchange">
							<view class="lang_mes">
								{{$t(lan)}}
							</view>
							<view class="iconfont icon-xiala xiala">

							</view>
						</view>
						<view class="langchange_box_body" style="top: 180rpx;">
							<view class="lang_box" v-for="(item,index) in langlist" :key="index"
								@click="lanchange(item)">
								{{$t('lang.' + item)}}
							</view>
						</view>
					</view>
					<view class="contact_box" @click="gotowburl">
						<view class="zzc">

						</view>
						<view class="contxt">
							{{$t('ContactUs')}}
						</view>
					</view>
				</view>
			</view>
		</header>
		<view v-else class="appheader">
			<view class="left_apphaderlogo" @click="goback">
				<view class="logo_appbox">
					<image src="../../static/image/logo3x.png" mode=""></image>
				</view>
				<view class="appname_box">
					<view class="appname_t">
						{{$t('logotit')}}
					</view>
					<view class="appname_b">
						{{$t('logotitmes')}}
					</view>
				</view>
			</view>
			<!-- <view class="hotapp_box" @click="goback">
				<image src="../../static/image/Group 1533213339.png" mode=""></image>
				<view class="backmes">
					{{$t('homepage')}}
				</view>
				<view class="backimg">
					<image src="../../static/image/down/返回 1.png" mode=""></image>
				</view>
			</view> -->
		</view>
		<!-- 		<view v-show="langch" class="langchange_box_body">
			<view class="lang_box" v-for="(item,index) in langlist" :key="index" @click="lanchange(item)">
				{{$t('lang.' + item)}}
			</view>
		</view> -->
		<view v-show="ispc" class="wordewide_boxbody" style="margin-top: 230rpx;">
			<view class="wordbanner_boxbody"></view>
			<view class="download_type_boxbody">
				<view class="download_type_mesboxbody">
					<view class="down_toptit">
						{{$t('down.tit1')}}
						<view style="color: #BAFC51;display: inline;">{{$t('down.tit2')}}</view>
					</view>
					<view class="down_topmes">
						{{$t('down.tit3')}}
					</view>
					<view class="down_typeboxbody" style="align-items: center;">
						<image src="../../static/image/down/Button.png" style="width: 388rpx;" mode="widthFix"></image>
						<image src="../../static/image/down/Button_margin.png" style="width: 388rpx;" mode="widthFix">
						</image>
						<image src="../../static/image/down/Button_margin (1).png" style="width: 388rpx;"
							mode="widthFix" @click="handleDown"></image>
						<image src="../../static/image/down/div.hover-hide_margin.png" style="width: 112rpx;"
							mode="widthFix"></image>
					</view>
				</view>
				<view class="qximg_boxbody">
					<image src="../../static/image/down/image (1).png" mode=""></image>
				</view>
			</view>
		</view>
		<view v-show="ispc" class="ztbox_body">
			<view class="zt_topimg_box">
				<image src="../../static/image/down/Group 1533213411 (1).png" mode="widthFix"
					style="position: absolute;top: 0;"></image>
				<view class="ztimgmes_boxbody">
					<view class="zt1">
						{{$t('down.Journey.tit1')}}
					</view>
					<view class="zt2">
						{{$t('down.Journey.tit2')}}
					</view>
					<view class="zt3_boxbody">
						<view class="zt3_box">
							<view class="zt3_t">
								{{$t('down.Journey.stepdata[0].tit1')}}
							</view>
							<view class="zt3_b">
								{{$t('down.Journey.stepdata[0].tit2')}}
							</view>
						</view>
						<view class="" style="height: 100%;width: auto;">
							<image src="../../static/image/down/Img_margin.png" mode="heightFix"></image>
						</view>
						<view class="zt3_box">
							<view class="zt3_t">
								{{$t('down.Journey.stepdata[1].tit1')}}
							</view>
							<view class="zt3_b">
								{{$t('down.Journey.stepdata[1].tit2')}}
							</view>
						</view>
						<view class="" style="height: 100%;width: auto;">
							<image src="../../static/image/down/Img_margin (1).png" mode="heightFix"></image>
						</view>
						<view class="zt3_box">
							<view class="zt3_t">
								{{$t('down.Journey.stepdata[2].tit1')}}
							</view>
							<view class="zt3_b">
								{{$t('down.Journey.stepdata[2].tit2')}}
							</view>
						</view>
					</view>
				</view>
			</view>
			<view class="zt_botimg_box">
				<view class="botimgbox">
					<image src="../../static/image/down/div.box (4).png" mode="" style="position: absolute;top: 0;">
					</image>
					<view class="ztbotimgmes">
						<view class="tm">
							{{$t('down.Journey.bean[0].tit1')}}
						</view>
						<view class="tm1">
							{{$t('down.Journey.bean[0].tit2')}}
						</view>
					</view>
				</view>
				<view class="botimgbox">
					<image src="../../static/image/down/div.box (5).png" mode="" style="position: absolute;top: 0;">
					</image>
					<view class="ztbotimgmes" style="color: white; ">
						<view class="tm">
							{{$t('down.Journey.bean[1].tit1')}}
						</view>
						<view class="tm1">
							{{$t('down.Journey.bean[1].tit2')}}
						</view>
					</view>
				</view>

			</view>
		</view>
		<view v-show="ispc" class="bottom_joinbo">
			<!-- <image src="../../static/image/Footer.png" mode=""></image> -->
			<view class="dh_menu_boxbody">
				<view class="dhtit1">
					{{$t('dhmenu[0].tit')}}
				</view>
				<view class="dhtit2">
					{{$t('dhmenu[0].mes1')}}
				</view>
				<view class="dhtit2">
					{{$t('dhmenu[0].mes2')}}
				</view>
			</view>
			<view class="dh_menu_boxbody">
				<view class="dhtit1">
					{{$t('dhmenu[1].tit')}}
				</view>
				<view class="dhtit2">
					{{$t('dhmenu[1].mes1')}}
				</view>
				<view class="dhtit2">
					{{$t('dhmenu[1].mes2')}}
				</view>
			</view>
			<view class="dh_menu_boxbody">
				<view class="dhtit1">
					{{$t('FollowUs')}}
				</view>
				<view class="dhiconboxbody">
					<view class="dhicon" style="margin-right: 17px;" @click="gotowburl">
						<image src="../../static/image/join/Item.png" mode=""></image>
					</view>
					<view class="dhicon" @click="gotowburl">
						<image src="../../static/image/join/Item (1).png" mode=""></image>
					</view>
				</view>
			</view>
			<view class="Subscribe_boxbody">
				<view class="Subscribe_topbox">
					{{$t('dhmenu[2].tit')}}
					<view class="sub_box">
						{{$t('dhmenu[2].mes1')}}
					</view>
				</view>
				<view class="subtit">
					{{$t('dhmenu[2].mes2')}}
				</view>
			</view>
		</view>
		<view v-show="ispc === false" class="app_topmes_boxbody">
			<view class="app_tbannerbox">
				<image src="https://pic1.imgdb.cn/item/680628a658cb8da5c8bcc4b7.png" mode="widthFix"></image>
			</view>
			<view class="app_dtoptit">
				{{$t('down.tit1')}}
				<view style="color: #BAFC51;display: inline;">{{$t('down.tit2')}}</view>
			</view>
			<view class="app_dtopmes">
				{{$t('down.tit3')}}
			</view>
		</view>
		<view v-show="ispc === false" class="app_dcen_imgbox">
			<image src="../../static/image/down/Group 1533213409 (2).png" mode="widthFix"></image>
		</view>
		<view v-show="ispc === false" class="app_dbotboxbody">
			<view class="app_dfristboxbody">
				<view class="app_ddowntyebox">
					<image src="../../static/image/down/Button (1).png" mode="heightFix"></image>
					<image src="../../static/image/down/Button (2).png" mode="heightFix" @click="handleDown"></image>
				</view>
				<view class="app_ddimgbox">
					<view class="appddimgmes_boxbody">
						<view class="tt1">
							{{$t('down.Journey.tit1')}}
						</view>
						<view class="tt2">
							{{$t('down.Journey.tit2')}}
						</view>
						<view class="tt3boxbody">
							<view class="zt3_box">
								<view class="zt3_t">
									{{$t('down.Journey.stepdata[0].tit1')}}
								</view>
								<view class="zt3_b">
									{{$t('down.Journey.stepdata[0].tit2')}}
								</view>
							</view>
							<view class="" style="height: 100%;width: 100rpx;">
								<image src="../../static/image/down/Img_margin.png"></image>
							</view>
							<view class="zt3_box">
								<view class="zt3_t">
									{{$t('down.Journey.stepdata[1].tit1')}}
								</view>
								<view class="zt3_b">
									{{$t('down.Journey.stepdata[1].tit2')}}
								</view>
							</view>
							<view class="" style="height: 100%;width: 100rpx;">
								<image src="../../static/image/down/Img_margin (1).png" mode=""></image>
							</view>
							<view class="zt3_box">
								<view class="zt3_t">
									{{$t('down.Journey.stepdata[2].tit1')}}
								</view>
								<view class="zt3_b">
									{{$t('down.Journey.stepdata[2].tit2')}}
								</view>
							</view>
						</view>
					</view>
					<image src="../../static/image/down/image.png" mode="widthFix"
						style="position: absolute;width: 100%;top: 35%;left: 0;"></image>
				</view>

			</view>
			<view class="app_ddimgbox1">
				<view class="ddimgbox">
					<image src="../../static/image/down/div.box (6).png" style="position: absolute;top: 0;"></image>
					<view class="ddimgboxmes_body">
						<view class="tm">
							{{$t('down.Journey.bean[0].tit1')}}
						</view>
						<view class="tm1">
							{{$t('down.Journey.bean[0].tit2')}}
						</view>
					</view>
				</view>
				<view class="ddimgbox">
					<image src="../../static/image/down/div.box (7).png" style="position: absolute;top: 0;"></image>
					<view class="ddimgboxmes_body" style="color: white;">
						<view class="tm">
							{{$t('down.Journey.bean[1].tit1')}}
						</view>
						<view class="tm1">
							{{$t('down.Journey.bean[1].tit2')}}
						</view>
					</view>
				</view>
			</view>
			<view class="bottom_appdbot">
				<!-- <image src="../../static/image/Footer1.png" mode="widthFix"></image> -->
				<view class=""
					style="width: 100%;height: 100%;position: relative;display: flex;flex-direction: column;background-color: #000;padding-bottom: 120rpx;">
					<!-- 					<image src="../../static/image/Footer (2).png" mode=""></image>
					<view class=""
						style="display: flex;width: 100%;height: 2.5rem;flex-direction: row;align-items: center;color: rgba(255, 255, 255, 0.5);position: absolute;bottom: 0;justify-content: center;font-size: 20rpx;">
						<view class="" style="display: inline;margin-right: 30rpx;">
							Privacy Policy
						</view>Copyright © 2025 Funshot
					</view> -->
					<view class="app_bot_dhboxbody">
						<view class="logoboxbody">
							<view class="logobox">
								<image src="../../static/image/SVG1.png" mode=""></image>
							</view>
							<view class="logotit_box">
								<view class="toptit">
									{{$t('appbot.FunshotApp')}}
								</view>
								<view class="bottit">
									{{$t('appbot.Generation')}}
								</view>
							</view>
						</view>
						<view class="dhboxbody">
							<view class="ttit">
								{{$t('appbot.list[0].tit1')}}
							</view>
							<view class="ctit">
								{{$t('appbot.list[0].tit2')}}
							</view>
							<view class="ctit" style="margin-top: 0;">
								{{$t('appbot.list[0].tit3')}}
							</view>
						</view>
						<view class="dhboxbody">
							<view class="ttit">
								{{$t('appbot.list[1].tit1')}}
							</view>
							<view class="ctit">
								{{$t('appbot.list[1].tit2')}}
							</view>
							<view class="ctit" style="margin-top: 0;">
								{{$t('appbot.list[1].tit3')}}
							</view>
						</view>
						<view class="dhboxbody">
							<view class="ttit">
								{{$t('appbot.list[2].tit1')}}
							</view>
							<view class="ctit">
								{{$t('appbot.list[2].tit2')}}
							</view>
							<view class="ctit" style="margin-top: 0;">
								{{$t('appbot.list[2].tit3')}}
							</view>
						</view>
						<view class="" style="width: 100%;height: auto;">
							<image src="../../static/image/div.box (8).png" mode="widthFix"></image>
						</view>
					</view>
					<view class=""
						style="width: 100%;height: auto;padding: 40rpx 0;box-sizing: border-box;background-color: #000;font-size: 20rpx;color: rgba(255, 255, 255, .5);display: flex;align-items: center;justify-content: center;">
						<view class="" style="display: inline;margin-right: 30rpx;">
							{{$t('appbot.Privacy')}}
						</view>{{$t('appbot.Copyright')}}
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script setup>
	import {
		onLoad,
		onReady,
		onShow
	} from '@dcloudio/uni-app';
	import {
		ref,
		reactive,
		onMounted,
		onBeforeMount,
		watch
	} from 'vue';
	import {
		useI18n
	} from 'vue-i18n';
	const {
		t,
		locale
	} = useI18n();

	const title = ref('hello')
	const menulist = ref(['Download']) //'Overview', 'Performance', 'Features', 'Advantages', 'Join', 
	const menuactive = ref(5)
	const langlist = ref(['Indonesian', 'Eenglish', 'Chinese'])
	const langch = ref(false)
	const lan = ref('lang.Eenglish')
	const ispc = ref(null)
	const windowWidth = ref(window.innerWidth);
	onBeforeMount(() => {
		if (uni.getStorageSync('lan')) {
			lan.value = 'lang.' + uni.getStorageSync('lan')
		}
		console.log(uni.getStorageSync('ispc'));
		ispc.value = uni.getStorageSync('ispc')
	})
	onMounted(() => {
		window.addEventListener('resize', updateWindowWidth);
		// Create a script element for OpenInstall
		const s = document.createElement('script');
		s.type = 'text/javascript';
		s.src = '//res.openinstalljs.com/openinstall-l23zgl.js';

		// s.addEventListener('load', () => {
		// 	// Using uni API instead of direct browser APIs
		// 	if (typeof OpenInstall !== 'undefined') {
		// 		var data = OpenInstall.parseUrlParams();
		// 		new OpenInstall({
		// 			appKey: "l23zgl", // Your OpenInstall appKey
		// 			onready: function() {
		// 				var m = this;
		// 				// Use uni.querySelector instead of document.getElementById
		// 				var button = uni.getElementById('downloadButton');

		// 				m.schemeWakeup();

		// 				// Use UniApp event handling
		// 				if (button) {
		// 					button.addEventListener('click', function() {
		// 						console.log(222222);
		// 						m.wakeupOrInstall();
		// 						return false;
		// 					});
		// 				}
		// 			}
		// 		}, data);
		// 	}
		// }, false);

		// Append to document using UniApp approach
		// In UniApp-X, we can still use document.head
		document.head.appendChild(s);
	})
	onShow(() => {
		checkAndSetBodyClass()
	})
	watch(windowWidth, (newVal) => {
		console.log('新的窗口宽度:', newVal);
		checkAndSetBodyClass()
		// 在这里处理宽度变化逻辑
	});
	const leftchoseinx = ref(0)

	const handleDown = () => {
		if (typeof OpenInstall !== 'undefined') {
			var data = OpenInstall.parseUrlParams();
			new OpenInstall({
				appKey: "l23zgl", // Your OpenInstall appKey
				onready: function() {
					var m = this;

					m.schemeWakeup();
					m.wakeupOrInstall();
				}
			}, data);
		}
	}

	const updateWindowWidth = () => {
		windowWidth.value = window.innerWidth;
	};

	const checkAndSetBodyClass = () => {
		const width = window.innerWidth;
		const body = document.body;

		// 移除已有的类，然后根据需要添加新的类
		body.className = ''; // 清空所有class
		if (width < 960) {
			body.classList.add('mobile');
			uni.setStorageSync('ispc', false)
			ispc.value = false
		} else {
			body.classList.add('pc');
			uni.setStorageSync('ispc', true)
			ispc.value = true
		}
	}


	const lanchange = (item) => {
		console.log('lan', item);
		uni.setStorageSync('lan', item)
		if (item === 'Chinese') {
			uni.setStorageSync('lang', 'zh')
			location.reload();
		} else if (item === 'Eenglish') {
			uni.setStorageSync('lang', 'en')
			location.reload();
		} else {
			uni.setStorageSync('lang', 'id')
			location.reload();
		}

	}


	const showlangchange = () => {
		langch.value = !langch.value
	}

	const menua = (index) => {
		menuactive.value = index
		return
		if (index === 5) {
			uni.navigateTo({
				url: '/pages/download/index'
			})
		} else if (index === 4) {
			uni.navigateTo({
				url: '/pages/join/index'
			})
		} else if (index === -1) {
			uni.navigateTo({
				url: '/pages/index/index'
			})
		} else {
			uni.setStorageSync('menuainx', index)
			uni.navigateTo({
				url: '/pages/index/index'
			})
		}
	}
	const menum = (index) => {
		menuactive.value = index
		// lastinx.value = index
	}
	const menul = () => {
		console.log('移出');
		// setTimeout(function() {
		// 	if (lastinx.value === menuactive.value) {
		menuactive.value = 5
		// 	}
		// }, 500)
	}
	const gotowburl = () => {
		window.location.href = 'https://linktr.ee/funshot_io';
	}
	const leftcho = (index) => {
		leftchoseinx.value = index
	}
	const tbclick = (index) => {
		tbinx.value = index
	}
	const flfclick = (index) => {
		flfinx.value = index
	}
	const aliclick = (index) => {
		aliinx.value = index
	}
	const fullxzclick = (index) => {
		fullxzinx.value = index
	}
	const lanclick = (index) => {
		lchaninx.value = index
	}

	const swiperchang = (e) => {
		console.log('e', e.detail.current);
		currentinx.value = e.detail.current
	}
	const goback = () => {
		uni.reLaunch({
			url: '/pages/index/index'
		})
	}
</script>

<style>

</style>

<style lang="scss">
	@import '../../static/scss/index.scss';

	.spswiper ::v-deep .uni-swiper-dots-horizontal {
		left: 10% !important;
	}

	.spswiper ::v-deep .uni-swiper-dot {
		width: 30rpx !important;
		height: 8rpx !important;
		border-radius: 6rpx !important;
	}

	.spswiper ::v-deep .uni-swiper-dot-active {
		width: 100rpx !important;
		background-color: #BAFC51;
	}

	.spswiper1 ::v-deep .uni-swiper-dot-active {
		// width: 100rpx !important;
		background-color: #BAFC51;
	}

	.botsscroll ::v-deep .uni-scroll-view-content {
		display: -webkit-inline-box !important;
		// flex-direction: row !important;
	}
</style>